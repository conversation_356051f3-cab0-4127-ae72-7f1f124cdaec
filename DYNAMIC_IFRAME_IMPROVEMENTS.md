# 动态iframe划词功能改进

## 问题描述
用户反馈动态插入的iframe（如弹框中的iframe）无法正常触发划词功能。

## 问题分析

### 原始实现的局限性
1. **观察范围有限**: 只观察`document.body`，弹框可能插入到其他位置
2. **Shadow DOM盲区**: 无法检测Shadow DOM中的iframe
3. **异步加载问题**: iframe内容可能需要更长时间加载
4. **单次检查**: 只在DOM变化时检查，可能错过延迟加载的iframe

## 改进方案

### 1. 扩大iframe搜索范围
```typescript
// 获取页面中所有的iframe，包括Shadow DOM中的
_getAllIframes(): HTMLIFrameElement[] {
  const iframes: HTMLIFrameElement[] = [];
  
  // 获取主文档中的iframe
  const mainIframes = document.querySelectorAll("iframe");
  mainIframes.forEach(iframe => iframes.push(iframe as HTMLIFrameElement));
  
  // 递归搜索Shadow DOM中的iframe
  const searchShadowDOM = (root: Document | ShadowRoot) => {
    // ... 递归搜索逻辑
  };
  
  searchShadowDOM(document);
  return iframes;
}
```

### 2. 改进MutationObserver配置
```typescript
// 观察整个文档而不仅仅是body
mainObserver.observe(document.documentElement, {
  childList: true,
  subtree: true,
});
```

### 3. 添加定期检查机制
```typescript
// 定期检查新的iframe（处理异步加载的情况）
const intervalId = setInterval(() => {
  const currentIframes = this._getAllIframes();
  currentIframes.forEach((iframe) => {
    // 检查是否已经处理过这个iframe
    if (!iframe.hasAttribute("data-sino-iframe-processed")) {
      iframe.setAttribute("data-sino-iframe-processed", "true");
      this._setupIframeSelectionListener(iframe);
    }
  });
}, 1000);
```

### 4. 增强iframe监听器设置
```typescript
_setupIframeSelectionListener(iframe: HTMLIFrameElement) {
  // 添加调试日志
  console.debug("尝试设置iframe选择监听器:", iframe.src || iframe.getAttribute("srcdoc")?.substring(0, 50) + "...");
  
  // 检查文档加载状态
  if (iframeDoc.readyState !== "complete") {
    iframeDoc.addEventListener('DOMContentLoaded', () => {
      this._injectSelectionScript(iframe, iframeDoc);
    }, { once: true });
    return;
  }
  
  // 设置超时重试机制
  setTimeout(() => {
    if (!iframe.hasAttribute("data-sino-script-injected")) {
      console.debug("iframe加载超时，强制重试");
      this._setupIframeSelectionListener(iframe);
    }
  }, 2000);
}
```

### 5. 改进脚本注入
```typescript
_injectSelectionScript(iframe: HTMLIFrameElement, iframeDoc: Document) {
  // 添加更多调试信息
  console.debug("开始向iframe注入选择监听脚本");
  
  // 改进的位置计算
  const absoluteRect = {
    top: rect.top + iframeRect.top + (window.parent.scrollY || 0),
    left: rect.left + iframeRect.left + (window.parent.scrollX || 0),
    // ... 其他坐标
  };
  
  // 发送就绪消息
  window.parent.postMessage({
    type: "iframe-listener-ready",
    iframeId: (window.frameElement && window.frameElement.id) || 'iframe-' + Date.now(),
    timestamp: Date.now()
  }, "*");
}
```

### 6. 增强消息处理
```typescript
// 监听来自iframe的消息
this._listeners.add(window, "message", (event) => {
  if (event.data?.type === "iframe-text-selected") {
    console.debug("主页面收到iframe选择消息:", event.data);
    this._handleIframeSelection(event.data);
  } else if (event.data?.type === "iframe-selection-cleared") {
    console.debug("主页面收到iframe清除选择消息");
    this._onClearSelection();
  } else if (event.data?.type === "iframe-listener-ready") {
    console.debug("iframe监听器就绪:", event.data.iframeId);
  }
});
```

## 技术特点

### 1. 全面检测
- ✅ 主文档中的iframe
- ✅ Shadow DOM中的iframe
- ✅ 动态创建的iframe
- ✅ 延迟加载的iframe
- ✅ 弹框中的iframe

### 2. 多重保障
- **MutationObserver**: 实时检测DOM变化
- **定期检查**: 每秒检查一次新iframe
- **重试机制**: 加载失败时自动重试
- **状态标记**: 避免重复处理同一iframe

### 3. 调试友好
- **详细日志**: 记录每个步骤的执行情况
- **状态追踪**: 跟踪iframe的处理状态
- **错误处理**: 优雅处理各种异常情况

### 4. 性能优化
- **去重机制**: 避免重复注入脚本
- **状态缓存**: 记录已处理的iframe
- **资源清理**: 页面卸载时清理所有资源

## 测试验证

### 测试文件
- `dynamic-iframe-test.html`: 动态iframe测试页面

### 测试场景
1. **弹框iframe**: 在模态弹框中创建iframe
2. **内联iframe**: 动态插入到页面中的iframe
3. **延迟iframe**: 模拟异步加载的iframe
4. **多重iframe**: 同时存在多个动态iframe

### 验证步骤
1. 打开测试页面
2. 在主页面文本中测试划词功能
3. 点击按钮创建不同类型的动态iframe
4. 在每个iframe中测试划词功能
5. 查看控制台日志确认检测过程

## 调试信息

### 控制台日志
```javascript
// iframe检测日志
"尝试设置iframe选择监听器: ..."
"iframe文档未就绪，等待加载完成"
"iframe加载完成，重新尝试设置监听器"
"开始向iframe注入选择监听脚本"
"iframe脚本注入成功"

// 消息通信日志
"主页面收到iframe选择消息: ..."
"iframe监听器就绪: iframe-123456"
```

### 检查方法
```javascript
// 检查iframe数量
document.querySelectorAll('iframe').length

// 检查已处理的iframe
document.querySelectorAll('iframe[data-sino-iframe-processed]').length

// 检查已注入脚本的iframe
document.querySelectorAll('iframe[data-sino-script-injected]').length
```

## 兼容性

### 支持的场景
- ✅ 弹框中的iframe
- ✅ 动态创建的iframe
- ✅ 延迟加载的iframe
- ✅ Shadow DOM中的iframe
- ✅ 多层嵌套的iframe

### 限制
- ❌ 跨域iframe（安全限制）
- ❌ 沙盒iframe（权限限制）

## 性能影响

### 优化措施
- 使用防抖机制减少频繁检查
- 状态标记避免重复处理
- 定时器在页面卸载时清理
- 错误处理避免脚本崩溃

### 资源消耗
- 每秒一次的定期检查（轻量级）
- MutationObserver的DOM监听
- 少量的调试日志输出

## 总结

通过这些改进，动态iframe的划词功能现在能够：

1. **自动检测**: 无论iframe如何创建都能被检测到
2. **实时响应**: 动态创建的iframe立即生效
3. **容错处理**: 处理各种异常和边界情况
4. **调试友好**: 提供详细的日志信息
5. **性能优化**: 避免重复处理和资源浪费

这些改进确保了无论iframe是如何创建的（弹框、动态插入、延迟加载等），都能正常触发划词功能。
