export const knowdgeSVGIcon = {
  // 知识库
  mainImage1: (
    <svg width="16px" height="16px" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
      <g id="0.2.素材" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
        <g id="素材切图" transform="translate(-592.000000, -442.000000)">
          <g id="知识库" transform="translate(592.000000, 442.000000)">
            <path d="M0,0 L20,0 L20,20 L0,20 L0,0 Z" id="路径" fill="#FFFFFF" opacity="0"></path>
            <g id="Icon" transform="translate(3.333435, 2.500000)">
              <path
                d="M1.24761631,15 C0.55857718,15 0,14.4250663 0,13.7158489 L0,1.28415227 C0,0.57493493 0.558576584,0 1.24761571,0 L12.9190509,0 C13.60809,0 14.1666667,0.574934234 14.1666667,1.28415157 L14.1666667,13.7158489 C14.1666667,14.4250663 13.6080908,15 12.9190516,15 L1.24761631,15 Z"
                id="路径"
                className="stroke-color"
                strokeLinejoin="round"
                strokeDasharray="0,0"
                fillRule="nonzero"
              ></path>
              <line
                x1="5.33333333"
                y1="4.26666667"
                x2="8.66666667"
                y2="4.26666667"
                id="Vector"
                className="stroke-color"
                strokeLinejoin="round"
                strokeDasharray="0,0"
              ></line>
              <line
                x1="5.33333333"
                y1="7.6"
                x2="8.66666667"
                y2="7.6"
                id="Vector"
                className="stroke-color"
                strokeLinejoin="round"
                strokeDasharray="0,0"
              ></line>
              <line
                x1="5.33333333"
                y1="10.9333333"
                x2="8.66666667"
                y2="10.9333333"
                id="Vector"
                className="stroke-color"
                strokeLinejoin="round"
                strokeDasharray="0,0"
              ></line>
              <line
                x1="3.12906901"
                y1="0.19927969"
                x2="3.12906901"
                y2="14.7770515"
                id="路径"
                className="stroke-color"
                strokeDasharray="0,0"
              ></line>
              <polyline
                id="路径-3"
                className="stroke-color"
                strokeLinejoin="round"
                points="9.41656494 0 9.41656494 3 10.9165649 1.75 12.1665649 3 12.1665649 0.25"
              ></polyline>
            </g>
          </g>
        </g>
      </g>
    </svg>
  ),

  pdf: (
    <svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg">
      <g id="0.2.素材" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
        <g id="素材切图" transform="translate(-510.000000, -846.000000)">
          <g id="PPT" transform="translate(510.000000, 846.000000)">
            <rect id="矩形" fill="#FA541C" x="0" y="0" width="24" height="24" rx="3.2"></rect>
            <g id="file-excel-filled" transform="translate(7.000000, 6.000000)" fill="#FFFFFF" fillRule="nonzero">
              <g id="file-p-p-t-filled">
                <rect id="矩形" opacity="0" x="0" y="0" width="12" height="12"></rect>
                <path
                  d="M5.834,0 C8.351,0 10,1.60464 10,3.93352 C10,6.27232 8.348875,7.85852 5.825125,7.85852 L3.35525,7.85852 L3.35525,11.52 C3.35525,11.78508 3.13141667,12 2.85525,12 L1.5,12 C1.223875,12 1,11.78508 1,11.52 L1,0.48 C1,0.21492 1.223875,0 1.5,0 Z M5.53120833,1.92868 L3.35525,1.92868 L3.35525,5.94696 L4.80970833,5.94696 C6.802,5.94696 7.609125,5.42948 7.609125,3.93352 C7.609125,2.65172 6.85425,1.92868 5.53120833,1.92868 Z"
                  id="形状结合"
                ></path>
              </g>
            </g>
          </g>
        </g>
      </g>
    </svg>
  ),
  excel: (
    <svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg">
      <g id="0.2.素材" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
        <g id="素材切图" transform="translate(-474.000000, -846.000000)">
          <g id="execl" transform="translate(474.000000, 846.000000)">
            <rect id="矩形" fill="#52C41A" x="0" y="0" width="24" height="24" rx="3.2"></rect>
            <g id="file-excel-filled" transform="translate(7.000000, 6.000000)" fill="#FFFFFF" fillRule="nonzero">
              <path
                d="M7.61206137,0.23356 L5.09141144,4.32584 L2.54510866,0.2318 C2.45564561,0.0879412159 2.29524257,0 2.1222278,0 L0.53953339,0 C0.446165354,0 0.354708305,0.0257000268 0.27575785,0.07412 C0.0450057235,0.21564 -0.0239646213,0.51208 0.121717217,0.73624 L3.51188761,5.95312 L0.0757644385,11.2646 C0.0262655059,11.3411082 0,11.4296359 0,11.52 C0,11.78508 0.221240396,12 0.494115903,12 L1.91329913,12 C2.08455774,12 2.24360211,11.9138461 2.33362706,11.77232 L4.91567089,7.71336 L7.48054419,11.77116 C7.57043284,11.9133729 7.72990653,12 7.90169564,12 L9.44523137,12 C9.53989582,12 9.63256642,11.973574 9.71221866,11.92388 C9.94185903,11.78068 10.0084823,11.48372 9.8610299,11.26068 L6.40917738,6.03864 L9.92135322,0.73984 C9.97270112,0.662356057 10,0.572151368 10,0.48 C10,0.21492 9.77880078,0 9.5058841,0 L8.03609517,0 C7.8623189,0 7.70132218,0.0886959134 7.61210255,0.23356 L7.61206137,0.23356 Z"
                id="路径"
              ></path>
            </g>
          </g>
        </g>
      </g>
    </svg>
  ),
  ppt: (
    <svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1">
      <g id="0.2.素材" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
        <g id="素材切图" transform="translate(-510.000000, -846.000000)">
          <g id="PPT" transform="translate(510.000000, 846.000000)">
            <rect id="矩形" fill="#FA541C" x="0" y="0" width="24" height="24" rx="3.2"></rect>
            <g id="file-excel-filled" transform="translate(7.000000, 6.000000)" fill="#FFFFFF" fillRule="nonzero">
              <g id="file-p-p-t-filled">
                <rect id="矩形" opacity="0" x="0" y="0" width="12" height="12"></rect>
                <path
                  d="M5.834,0 C8.351,0 10,1.60464 10,3.93352 C10,6.27232 8.348875,7.85852 5.825125,7.85852 L3.35525,7.85852 L3.35525,11.52 C3.35525,11.78508 3.13141667,12 2.85525,12 L1.5,12 C1.223875,12 1,11.78508 1,11.52 L1,0.48 C1,0.21492 1.223875,0 1.5,0 Z M5.53120833,1.92868 L3.35525,1.92868 L3.35525,5.94696 L4.80970833,5.94696 C6.802,5.94696 7.609125,5.42948 7.609125,3.93352 C7.609125,2.65172 6.85425,1.92868 5.53120833,1.92868 Z"
                  id="形状结合"
                ></path>
              </g>
            </g>
          </g>
        </g>
      </g>
    </svg>
  ),
  txt: (
    <svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg">
      <g id="0.2.素材" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
        <g id="素材切图" transform="translate(-578.000000, -846.000000)">
          <g id="text-、" transform="translate(578.000000, 846.000000)">
            <rect id="矩形" fill="#722ED1" x="0" y="0" width="24" height="24" rx="3.2"></rect>
            <g id="file-word-filled" transform="translate(5.907404, 6.238281)" fill="#FFFFFF" fillRule="nonzero">
              <path
                d="M0,0.200000003 L0,2.8 C1.35270752e-17,2.91045695 0.0895430514,3 0.200000003,3 L1.3,3 C1.41045695,3 1.5,2.91045695 1.5,2.8 L1.5,1.5 L1.5,1.5 L5.25,1.5 L5.25,10.5 L3.2,10.5 C3.08954305,10.5 3,10.5895431 3,10.7 L3,11.8 C3,11.9104569 3.08954305,12 3.2,12 L8.8,12 C8.91045695,12 9,11.9104569 9,11.8 L9,10.7 C9,10.5895431 8.91045695,10.5 8.8,10.5 L6.75,10.5 L6.75,10.5 L6.75,1.5 L10.5,1.5 L10.5,2.8 C10.5,2.91045695 10.5895431,3 10.7,3 L11.8,3 C11.9104569,3 12,2.91045695 12,2.8 L12,0.200000003 C12,0.0895430514 11.9104569,-1.10320877e-16 11.8,0 L0.200000003,0 C0.0895430514,2.02906128e-17 -2.42335218e-16,0.0895430514 0,0.200000003 Z"
                id="路径"
              ></path>
            </g>
          </g>
        </g>
      </g>
    </svg>
  ),
  word: (
    <svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg">
      <title>word</title>
      <g id="0.2.素材" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
        <g id="素材切图" transform="translate(-546.000000, -846.000000)">
          <g id="word" transform="translate(546.000000, 846.000000)">
            <rect id="矩形" fill="#1677FF" x="0" y="0" width="24" height="24" rx="3.2"></rect>
            <g id="file-word-filled" transform="translate(5.860529, 7.531250)" fill="#FFFFFF" fillRule="nonzero">
              <path
                d="M6.00015068,3.1365 L7.73589661,9.70356667 C7.7821069,9.87841129 7.93832949,10 8.116805,10 L9.16126301,10 C9.33956138,10 9.49568551,9.87867701 9.5420728,9.70406667 L11.9864266,0.504066669 C11.9954388,0.470133149 12,0.435143028 12,0.400000002 C12,0.179100002 11.8234149,0 11.6056169,0 L10.4364023,0 C10.2526458,0 10.0932352,0.128702766 10.0520431,0.310333336 L8.54714273,6.94716667 L6.91176731,0.303133336 C6.86793434,0.125016488 6.71018612,0 6.5291828,0 L5.47118429,0 C5.29015646,0 5.13237295,0.124992374 5.08853405,0.303133336 L3.45644515,6.93403333 L1.94148804,0.309633336 C1.90003464,0.128341646 1.74078293,0 1.55729313,0 L0.394487305,0 C0.359917689,0 0.325497622,0.00460232197 0.292112015,0.0137000024 C0.0817743395,0.0710333357 -0.0429164636,0.290466669 0.0136117867,0.503800002 L2.45132685,9.7038 C2.49761471,9.87851615 2.65376337,10 2.83213664,10 L3.88349636,10 C4.06197187,10 4.21819446,9.87841129 4.26440474,9.70356667 L6.00015068,3.1365 Z"
                id="路径"
              ></path>
            </g>
          </g>
        </g>
      </g>
    </svg>
  ),
  hengMore: (
    <svg width="16px" height="16px" viewBox="0 0 20 20" version="1.1" xmlns="http://www.w3.org/2000/svg">
      <g id="0.2.素材" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
        <g id="素材切图" transform="translate(-623.000000, -544.000000)">
          <g id="3-Dot-Horizontal" transform="translate(623.000000, 544.000000)">
            <g id="Icon" transform="translate(2.291667, 8.333689)" stroke="#121212" strokeDasharray="0,0">
              <path
                d="M3.33333333,1.66666667 C3.33333333,2.58714125 2.58714125,3.33333333 1.66666667,3.33333333 C0.746192084,3.33333333 0,2.58714125 0,1.66666667 C0,0.746192084 0.746192084,0 1.66666667,0 C2.58714125,0 3.33333333,0.746192084 3.33333333,1.66666667 Z"
                id="路径"
              ></path>
              <path
                d="M9.375,1.66666667 C9.375,2.58714125 8.62880792,3.33333333 7.70833333,3.33333333 C6.78785875,3.33333333 6.04166667,2.58714125 6.04166667,1.66666667 C6.04166667,0.746192084 6.78785875,0 7.70833333,0 C8.62880792,0 9.375,0.746192084 9.375,1.66666667 Z"
                id="路径"
              ></path>
              <path
                d="M15.4166667,1.66666667 C15.4166667,2.58714125 14.6704746,3.33333333 13.75,3.33333333 C12.8295254,3.33333333 12.0833333,2.58714125 12.0833333,1.66666667 C12.0833333,0.746192084 12.8295254,0 13.75,0 C14.6704746,0 15.4166667,0.746192084 15.4166667,1.66666667 Z"
                id="路径"
              ></path>
            </g>
          </g>
        </g>
      </g>
    </svg>
  ),
  json: (
    <svg viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" width="24" height="24">
      <path
        d="M145.622 0c-44.799 0-79.998 36.812-79.998 81.61v860.78c0 44.798 35.2 81.61 79.998 81.61h732.782a81.97 81.97 0 0 0 81.61-81.61V324.804L657.61 0H145.622z"
        fill="#F17F53"
      ></path>
      <path
        d="M657.61 0v233.594c0 25.6 17.612 92.798 97.61 92.798h204.794L657.61 0zM109.117 659.875c1.613 0 2.867 0.614 3.815 1.868s1.664 2.765 2.176 4.583 0.819 3.737 0.972 5.862 0.23 4.045 0.23 5.862v5.709c2.1 3.302 4.788 6.886 8.013 10.726s6.963 7.45 11.187 10.803 8.78 6.144 13.721 8.397 10.215 3.379 15.82 3.379c5.991 0 11.623-1.408 16.87-4.199s10.138-6.527 14.618-11.187 8.653-9.983 12.441-15.974 7.22-12.236 10.266-18.661 5.76-12.877 8.09-19.277 4.377-12.287 6.066-17.689 3.046-10.086 4.045-14.028 1.69-6.682 2.099-8.167c2.611-10.598 4.813-20.76 6.681-30.437s2.765-19.251 2.765-28.646c0-2.304-0.691-3.61-2.099-3.891l-37.196 10.496a19.916 19.916 0 0 1-5.094 0.896c-3.789 0-6.963-1.203-9.523-3.61s-3.814-5.862-3.814-10.342 0.435-8.115 1.28-10.88 2.201-4.812 4.044-6.22 4.275-2.33 7.27-2.765 6.656-0.666 10.957-0.666h2.1c2.406-0.512 5.477-1.203 9.215-2.099s7.936-1.92 12.518-3.072l14.694-3.737 15.744-4.045c5.299-1.357 10.572-2.662 15.82-3.968a1637.284 1637.284 0 0 1 23.475-5.632l5.99-1.28a22.015 22.015 0 0 1 3.737-0.588c1.204 0 2.304 0.358 3.303 1.05s1.817 1.586 2.483 2.636a14.515 14.515 0 0 1 2.176 7.424c0 2.995-1.152 5.606-3.456 7.807s-5.197 4.122-8.704 5.786-7.321 3.097-11.468 4.352l-11.622 3.533c-3.61 1.1-6.682 2.175-9.216 3.225s-3.968 2.227-4.275 3.533c-0.512 4.403-0.999 9.292-1.51 14.694s-1.05 10.547-1.64 15.462c-0.204 1.203-0.613 4.096-1.279 8.704s-1.562 10.188-2.688 16.793-2.56 13.823-4.198 21.682-3.533 15.565-5.632 23.168c-2.304 8.294-5.325 17.151-9.062 26.546s-8.192 18.56-13.338 27.443-10.982 17.228-17.484 24.985-13.542 14.13-21.145 19.123a58.93 58.93 0 0 1-23.193 8.089 61.336 61.336 0 0 1-9.37 0.742c-5.196 0-10.828-0.69-16.87-2.099s-12.133-3.404-18.226-5.99-11.98-5.734-17.612-9.37-10.675-7.705-15.078-12.16-7.91-9.215-10.496-14.335-3.891-10.444-3.891-16.05c0-4.301 0.435-8.013 1.28-11.11s2.227-5.607 4.121-7.501 4.429-3.303 7.578-4.199 7.014-1.484 11.52-1.484z m196.501 33.458c3.405 1.203 6.886 2.611 10.419 4.198s7.219 3.123 11.033 4.583 7.782 2.662 11.93 3.686 8.575 1.51 13.286 1.51c5.503 0 11.059-0.64 16.64-1.945s11.084-3.226 16.434-5.786 10.47-5.657 15.385-9.292 9.395-7.885 13.491-12.672c4.506-5.094 8.243-9.728 11.238-13.875s5.427-7.859 7.27-11.11 3.175-6.041 3.968-8.397 1.203-4.377 1.203-6.067c0-2.508-0.844-4.684-2.56-6.527s-3.967-3.38-6.835-4.583-6.067-2.073-9.676-2.636-7.347-0.82-11.238-0.82c-2.304 0-4.992 0.077-8.09 0.23l-9.446 0.462-9.292 0.384a150.396 150.396 0 0 1-7.808 0.153c-3.302 0-6.733-0.384-10.265-1.126s-7.015-1.971-10.42-3.686-6.553-3.994-9.446-6.912a35.404 35.404 0 0 1-8.831-15.744 24.627 24.627 0 0 1-0.743-6.144c0-2.406 0.18-4.684 0.538-6.835s0.768-4.224 1.28-6.22c1.408-4.992 3.737-10.087 6.963-15.232s7.142-10.163 11.699-15.078 9.497-9.651 14.847-14.259 10.855-8.934 16.512-12.979 11.212-7.705 16.64-10.956 10.52-6.016 15.231-8.32c6.605-3.2 13.337-5.53 20.172-6.963s13.62-2.176 20.326-2.176c3.994 0 7.808 0.537 11.392 1.587s6.784 2.586 9.523 4.582 4.915 4.48 6.528 7.424 2.406 6.323 2.406 10.138c0 2.201-0.256 4.044-0.742 5.555s-1.152 2.713-1.946 3.686-1.664 1.613-2.636 2.022-1.92 0.589-2.919 0.589c-2.688 0-5.17-0.307-7.424-0.896s-4.428-1.28-6.527-2.022-4.199-1.46-6.298-2.1-4.403-0.972-6.912-0.972c-7.398 0-14.924 1.485-22.578 4.428s-15.104 6.707-22.349 11.239-14.105 9.625-20.556 15.231-12.236 11.06-17.33 16.358c-2.689 2.79-4.685 5.606-5.914 8.397s-1.869 5.452-1.869 7.961c0 3.712 1.613 6.605 4.813 8.704s7.09 3.149 11.699 3.149c2.688 0 5.76-0.052 9.139-0.154a796.14 796.14 0 0 0 20.76-0.845c3.354-0.153 6.324-0.23 8.935-0.23 6.195 0 12.16 0.87 17.843 2.637s10.751 4.326 15.155 7.73 7.935 7.63 10.572 12.672 3.968 10.829 3.968 17.331c0 3.994-0.589 8.038-1.792 12.16s-2.841 8.217-4.94 12.364-4.506 8.243-7.194 12.288-5.555 8.013-8.55 11.93c-5.402 6.988-11.418 13.593-18.073 19.788s-13.722 11.596-21.222 16.204-15.437 8.243-23.782 10.956-16.921 4.045-25.727 4.045a74.725 74.725 0 0 1-23.552-4.121c-3.89-1.357-7.577-3.047-11.033-5.095s-6.477-4.377-9.062-6.963-4.685-5.478-6.221-8.627-2.33-6.579-2.33-10.265c0-1.792 0.282-3.533 0.82-5.171s1.868-2.56 4.07-2.56z m191.534-41.701c1.792-7.5 4.403-15.18 7.808-23.014s7.398-15.718 12.006-23.628 9.728-15.667 15.385-23.321a316.869 316.869 0 0 1 57.983-59.928c3.2-2.202 6.144-3.661 8.857-4.352s5.555-1.05 8.55-1.05c3.2 0 6.042 0.486 8.55 1.434s4.993 2.662 7.501 5.17c0.794 0.897 1.639 1.69 2.56 2.407s1.741 1.05 2.56 1.05c0.205 0 0.461-0.23 0.743-0.666l0.972-1.587c0.359-0.614 0.768-1.229 1.28-1.869s1.101-1.126 1.792-1.433c1.69-0.794 3.738-1.332 6.067-1.588s4.736-0.384 7.117-0.384c4.506 0 8.857 0.82 13.056 2.484s7.91 3.967 11.11 6.963c7.603 7.09 13.363 15.59 17.33 25.497s5.914 20.556 5.914 31.948c0 10.188-1.51 21.196-4.505 32.997-2.612 10.393-6.323 20.505-11.187 30.31s-10.547 18.994-17.1 27.596-13.85 16.46-21.888 23.551-16.614 13.21-25.728 18.304-18.61 9.062-28.492 11.93-19.942 4.274-30.156 4.274c-9.497 0-18.022-1.459-25.573-4.352s-13.952-6.988-19.2-12.287-9.241-11.597-12.006-18.893-4.122-15.41-4.122-24.293c0-3.79 0.23-7.63 0.666-11.469s1.152-7.808 2.15-11.801z m25.344 10.06a63.23 63.23 0 0 0-1.639 13.491c0 5.606 0.973 10.521 2.919 14.771s4.71 7.808 8.243 10.65 7.808 4.991 12.748 6.45 10.368 2.176 16.281 2.176c6.989 0 14.131-1.1 21.376-3.302s14.335-5.402 21.222-9.6 13.542-9.241 19.942-15.155 12.313-12.595 17.766-20.095 10.29-15.641 14.54-24.448 7.68-18.15 10.265-28.056c0.999-3.61 1.716-7.347 2.176-11.239s0.666-7.654 0.666-11.238c0-5.81-0.64-11.34-1.946-16.64s-3.456-10.009-6.45-14.104-6.938-7.373-11.853-9.83-10.906-3.687-17.997-3.687c-0.998 0-1.766 0.435-2.33 1.28s-1.125 1.92-1.714 3.226a71.725 71.725 0 0 1-1.715 3.455 11.878 11.878 0 0 1-6.451 5.556 15.795 15.795 0 0 1-5.786 0.895c-1.69 0-3.2-0.383-4.505-1.126s-2.612-1.766-3.892-3.072l-1.51-1.433a1.971 1.971 0 0 0-1.357-0.538c-0.896 0-2.073 0.41-3.532 1.203s-2.944 1.741-4.506 2.842-3.046 2.252-4.505 3.456-2.586 2.252-3.38 3.148c-6.809 6.989-13.567 14.618-20.325 22.886s-12.98 16.768-18.662 25.574-10.675 17.664-14.925 26.623-7.27 17.561-9.164 25.881z m237.587-115.197c0.59-2.611 1.357-5.376 2.253-8.32s2.1-5.657 3.61-8.09 3.456-4.505 5.862-6.143 5.453-2.483 9.139-2.483c3.507 0 6.63 0.973 9.37 2.918s5.222 3.917 7.423 5.914c2.611 1.69 5.094 5.145 7.5 10.342s4.865 11.596 7.424 19.2 5.3 16.204 8.243 25.804 6.196 19.66 9.754 30.156 7.526 21.221 11.93 32.178 9.394 21.631 15 32.025l3.15 5.632a95.076 95.076 0 0 0 7.141 10.88c1.05 1.305 1.818 1.945 2.33 1.945 0.307 0 0.64-0.742 1.05-2.253s0.844-3.302 1.356-5.401l1.587-6.451a59.903 59.903 0 0 1 1.588-5.402 476.737 476.737 0 0 0 13.72-37.426c2.74-8.448 5.786-18.483 9.063-30.08a1560.127 1560.127 0 0 0 14.182-54.526c1.152-4.966 2.253-10.01 3.302-15.155s1.971-10.163 2.765-15.078 1.357-9.139 1.638-12.748a77.54 77.54 0 0 1 0.692-8.243c0.256-1.895 0.563-3.866 0.972-5.914s0.922-3.916 1.588-5.632 1.484-3.097 2.483-4.198 2.201-1.638 3.61-1.638 3.122 0.205 5.17 0.589 3.917 0.998 5.632 1.792c1.894 2.61 3.635 5.734 5.171 9.369s2.33 8.576 2.33 14.77c0 3.892-0.41 8.577-1.203 14.03s-1.818 11.288-3.072 17.56a634.864 634.864 0 0 1-8.935 38.246c-1.612 6.041-3.123 11.596-4.582 16.64s-2.662 9.215-3.686 12.517c-1.792 5.812-3.686 11.776-5.632 17.92s-4.096 12.595-6.451 19.353-4.89 13.9-7.654 21.452-5.837 15.616-9.216 24.217c-1.51 3.891-3.226 7.808-5.171 11.7s-4.173 7.423-6.682 10.572-5.324 5.708-8.473 7.654-6.681 2.918-10.572 2.918c-2.304 0-4.685-0.332-7.117-0.972s-4.531-1.972-6.22-3.968c-8.193-9.498-15.488-20.275-21.888-32.332s-12.134-24.652-17.177-37.81-9.523-26.445-13.414-39.91-7.45-26.316-10.65-38.63l-0.896-3.455c-0.41-1.408-0.768-2.816-1.126-4.275s-0.691-2.765-1.05-3.968-0.742-2.176-1.203-2.919-0.973-1.126-1.587-1.126-1.357 1.1-2.253 3.302-1.792 4.685-2.688 7.424-1.715 5.427-2.483 8.013l-1.433 4.94c-0.692 2.612-1.741 6.323-3.15 11.187l-4.735 16.435a1819.343 1819.343 0 0 1-5.709 19.353l-5.913 19.737a1165.027 1165.027 0 0 1-12.902 40.729l-3.891 12.364c-1.28 4.148-2.509 7.936-3.61 11.315s-1.792 5.658-2.099 6.759c-1.203 3.788-2.97 6.681-5.325 8.627s-4.735 2.918-7.116 2.918c-1.51 0-2.995-0.538-4.506-1.587s-2.816-2.33-3.968-3.815-2.099-3.123-2.841-4.863-1.127-3.328-1.127-4.736c0-0.794 0.256-2.074 0.743-3.815s1.305-4.736 2.406-8.934l3.891-15.462 44.338-143.638z"
        fill="#FFFFFF"
      ></path>
    </svg>
  ),
};
