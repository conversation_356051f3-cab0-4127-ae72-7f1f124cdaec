<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>划词功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 40px;
            line-height: 1.8;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 40px;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            background: #f8f9fa;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            border-left: 4px solid #007bff;
        }
        .test-section h3 {
            color: #007bff;
            margin-top: 0;
        }
        .highlight-text {
            background: #fff3cd;
            padding: 15px;
            border-radius: 6px;
            margin: 15px 0;
            border: 1px solid #ffeaa7;
        }
        .instructions {
            background: #d1ecf1;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            border-left: 4px solid #17a2b8;
        }
        .debug-info {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            padding: 15px;
            margin: 20px 0;
            font-family: monospace;
            font-size: 14px;
        }
        .status {
            padding: 10px;
            border-radius: 6px;
            margin: 10px 0;
            font-weight: bold;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🖱️ 划词功能测试页面</h1>
        
        <div class="instructions">
            <h3>📋 测试说明</h3>
            <p><strong>如何测试：</strong></p>
            <ol>
                <li>确保浏览器插件已加载</li>
                <li>在下面的文本区域中选择文字</li>
                <li>观察是否出现AdderToolbar（划词工具栏）</li>
                <li>查看控制台日志了解详细信息</li>
            </ol>
        </div>

        <div id="status-container">
            <div class="status info" id="loading-status">
                🔄 正在检测插件状态...
            </div>
        </div>

        <div class="test-section">
            <h3>📝 主页面文本测试</h3>
            <p>请选择下面的文字来测试划词功能：</p>
            <div class="highlight-text">
                这是一段用于测试划词功能的文本。请用鼠标选中这段文字，如果功能正常，应该会出现一个包含各种操作按钮的工具栏。这个工具栏通常包含创建便签、高亮、AI分析等功能。
            </div>
            <p>
                这里是另一段测试文本。划词功能是浏览器插件的核心功能之一，它允许用户快速对选中的文本进行各种操作，如创建注释、保存到笔记、进行AI分析等。请尝试选择这段文字来验证功能是否正常工作。
            </p>
        </div>

        <div class="test-section">
            <h3>🎯 长文本测试</h3>
            <div class="highlight-text">
                这是一段较长的测试文本，用于验证划词功能在处理长文本时的表现。在实际使用中，用户经常需要选择较长的文本段落进行操作，因此测试长文本的划词功能非常重要。这段文本包含了足够的内容来模拟真实的使用场景，包括多个句子和丰富的语义信息。请尝试选择这整段文字，或者选择其中的部分内容，来测试工具栏的显示位置和功能是否正常。
            </div>
        </div>

        <div class="test-section">
            <h3>🔤 特殊字符测试</h3>
            <p>测试包含特殊字符的文本选择：</p>
            <div class="highlight-text">
                这段文本包含各种特殊字符：@#$%^&*()，中英文混合 English text，数字123456，标点符号！？。、；：""''，以及一些特殊符号 ★☆♠♣♥♦ 等。请选择这段文字来测试划词功能对特殊字符的处理能力。
            </div>
        </div>

        <div class="debug-info">
            <h4>🔍 调试信息</h4>
            <div id="debug-log">
                等待操作...
            </div>
        </div>
    </div>

    <script>
        let debugLog = document.getElementById('debug-log');
        let statusContainer = document.getElementById('status-container');
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            debugLog.innerHTML += `<div>[${timestamp}] ${message}</div>`;
            debugLog.scrollTop = debugLog.scrollHeight;
            console.log(`[划词测试] ${message}`);
        }

        function updateStatus(message, type = 'info') {
            const statusDiv = document.createElement('div');
            statusDiv.className = `status ${type}`;
            statusDiv.textContent = message;
            statusContainer.appendChild(statusDiv);
            
            // 移除旧的loading状态
            const loadingStatus = document.getElementById('loading-status');
            if (loadingStatus) {
                loadingStatus.remove();
            }
        }

        // 检测插件是否加载
        function checkPluginStatus() {
            // 检查是否存在插件注入的元素
            const sidePanelElement = document.getElementById('shadow-side-panel');
            if (sidePanelElement) {
                updateStatus('✅ 插件已检测到', 'success');
                log('插件Shadow DOM元素已找到');
                
                // 检查Shadow Root
                if (sidePanelElement.shadowRoot) {
                    log('插件Shadow Root已创建');
                    
                    // 检查是否有annotator相关元素
                    const annotatorEl = sidePanelElement.shadowRoot.getElementById('annotator-stroke');
                    if (annotatorEl) {
                        log('Annotator元素已找到');
                        updateStatus('✅ 划词服务已初始化', 'success');
                    } else {
                        log('Annotator元素未找到');
                        updateStatus('⚠️ 划词服务可能未完全初始化', 'error');
                    }
                } else {
                    log('插件Shadow Root未找到');
                    updateStatus('⚠️ 插件Shadow Root未创建', 'error');
                }
            } else {
                updateStatus('❌ 插件未检测到', 'error');
                log('插件Shadow DOM元素未找到，请确保插件已正确加载');
            }
        }

        // 监听文本选择事件
        document.addEventListener('selectionchange', () => {
            const selection = window.getSelection();
            if (selection && selection.toString().trim()) {
                const selectedText = selection.toString().trim();
                log(`检测到文本选择: "${selectedText.substring(0, 30)}${selectedText.length > 30 ? '...' : ''}"`);
                
                // 检查是否有AdderToolbar出现
                setTimeout(() => {
                    const adderElements = document.querySelectorAll('[data-testid="adder"], .hypothesis-adder, .adder');
                    if (adderElements.length > 0) {
                        log('✅ AdderToolbar已显示');
                        updateStatus('✅ 划词工具栏正常显示', 'success');
                    } else {
                        log('❌ AdderToolbar未显示');
                        updateStatus('❌ 划词工具栏未显示，可能存在问题', 'error');
                    }
                }, 200);
            } else {
                log('文本选择已清除');
            }
        });

        // 监听iframe相关消息（如果有的话）
        window.addEventListener('message', (event) => {
            if (event.data?.type?.startsWith('iframe-')) {
                log(`收到iframe消息: ${event.data.type}`);
            }
        });

        // 页面加载完成后检查插件状态
        document.addEventListener('DOMContentLoaded', () => {
            log('页面加载完成');
            
            // 延迟检查插件状态，给插件时间初始化
            setTimeout(() => {
                checkPluginStatus();
            }, 1000);
            
            // 定期检查插件状态
            setInterval(() => {
                const sidePanelElement = document.getElementById('shadow-side-panel');
                if (sidePanelElement && !document.querySelector('.status.success')) {
                    checkPluginStatus();
                }
            }, 2000);
        });

        // 添加一些测试用的全局函数
        window.testSelection = function() {
            log('手动触发选择测试');
            const testText = document.querySelector('.highlight-text');
            if (testText) {
                const range = document.createRange();
                range.selectNodeContents(testText);
                const selection = window.getSelection();
                selection.removeAllRanges();
                selection.addRange(range);
                log('已选择测试文本');
            }
        };

        window.clearSelection = function() {
            window.getSelection().removeAllRanges();
            log('已清除选择');
        };

        log('测试页面初始化完成');
        log('请选择页面中的文字来测试划词功能');
    </script>
</body>
</html>
