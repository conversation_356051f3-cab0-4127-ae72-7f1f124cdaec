<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>动态iframe划词测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            line-height: 1.6;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
        }
        .section {
            background: #f5f5f5;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
        }
        .button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .button:hover {
            background: #0056b3;
        }
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }
        .modal-content {
            background-color: #fefefe;
            margin: 5% auto;
            padding: 20px;
            border: none;
            width: 80%;
            max-width: 800px;
            border-radius: 8px;
            position: relative;
        }
        .close {
            color: #aaa;
            float: right;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
            position: absolute;
            right: 15px;
            top: 10px;
        }
        .close:hover {
            color: black;
        }
        .iframe-container {
            margin-top: 20px;
            border: 2px solid #ddd;
            border-radius: 8px;
            overflow: hidden;
        }
        .iframe-header {
            background: #333;
            color: white;
            padding: 10px;
            font-weight: bold;
        }
        iframe {
            width: 100%;
            height: 400px;
            border: none;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }
        .test-text {
            background: white;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
            border-left: 4px solid #007bff;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>动态iframe划词功能测试</h1>
        
        <div class="section">
            <h2>主页面内容</h2>
            <div class="test-text">
                这是主页面的文本内容。请先在这里测试划词功能是否正常工作。
                选中这段文字应该会显示AdderToolbar。
            </div>
        </div>

        <div class="section">
            <h2>动态iframe测试</h2>
            <p>点击下面的按钮来创建不同类型的动态iframe：</p>
            
            <button class="button" onclick="createModalIframe()">创建弹框iframe</button>
            <button class="button" onclick="createInlineIframe()">创建内联iframe</button>
            <button class="button" onclick="createDelayedIframe()">创建延迟加载iframe</button>
            <button class="button" onclick="clearLog()">清空日志</button>
            
            <div id="dynamic-content"></div>
        </div>

        <div class="section">
            <h2>调试日志</h2>
            <div id="log" class="log">等待操作...</div>
        </div>
    </div>

    <!-- 弹框模板 -->
    <div id="modal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeModal()">&times;</span>
            <h3>弹框中的iframe</h3>
            <p>这是一个动态创建的弹框，包含iframe内容。请在iframe中选择文字测试划词功能。</p>
            <div id="modal-iframe-container"></div>
        </div>
    </div>

    <script>
        let logElement = document.getElementById('log');
        let iframeCounter = 0;

        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            logElement.innerHTML += `[${timestamp}] ${message}<br>`;
            logElement.scrollTop = logElement.scrollHeight;
            console.log(message);
        }

        function clearLog() {
            logElement.innerHTML = '日志已清空<br>';
        }

        // 监听iframe相关消息
        window.addEventListener('message', (event) => {
            if (event.data?.type?.startsWith('iframe-')) {
                log(`收到iframe消息: ${event.data.type} - ${event.data.selectedText?.substring(0, 30) || ''}...`);
            }
        });

        function createModalIframe() {
            log('创建弹框iframe...');
            
            const container = document.getElementById('modal-iframe-container');
            container.innerHTML = `
                <div class="iframe-container">
                    <div class="iframe-header">动态弹框iframe #${++iframeCounter}</div>
                    <iframe id="modal-iframe-${iframeCounter}" srcdoc="
                        <!DOCTYPE html>
                        <html>
                        <head>
                            <style>
                                body { 
                                    font-family: Arial, sans-serif; 
                                    padding: 20px; 
                                    line-height: 1.8; 
                                    background: #f0f8ff;
                                }
                                .content { 
                                    background: white; 
                                    padding: 20px; 
                                    border-radius: 8px; 
                                    margin: 15px 0;
                                    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
                                }
                                .highlight { 
                                    background: #fff3cd; 
                                    padding: 15px; 
                                    border-left: 4px solid #ffc107; 
                                    margin: 15px 0; 
                                }
                            </style>
                        </head>
                        <body>
                            <h3>🚀 动态弹框iframe内容</h3>
                            <div class='content'>
                                这是动态创建的弹框中的iframe内容。请尝试选中这段文字，AdderToolbar应该会出现在主页面中，位置对应iframe中的选择区域。
                            </div>
                            <div class='highlight'>
                                <strong>重要测试：</strong>这段文字用于测试动态创建的iframe是否能够正确触发划词功能。选择这里的文字应该能够在主页面显示工具栏。
                            </div>
                            <div class='content'>
                                弹框iframe测试：验证在弹框中动态插入的iframe是否能够被正确检测和处理。这是第三段测试内容。
                            </div>
                        </body>
                        </html>
                    "></iframe>
                </div>
            `;
            
            document.getElementById('modal').style.display = 'block';
            log(`弹框iframe #${iframeCounter} 已创建`);
        }

        function createInlineIframe() {
            log('创建内联iframe...');
            
            const container = document.getElementById('dynamic-content');
            const iframeId = ++iframeCounter;
            
            const iframeHtml = `
                <div class="iframe-container" style="margin-top: 20px;">
                    <div class="iframe-header">动态内联iframe #${iframeId}</div>
                    <iframe id="inline-iframe-${iframeId}" srcdoc="
                        <!DOCTYPE html>
                        <html>
                        <head>
                            <style>
                                body { 
                                    font-family: Arial, sans-serif; 
                                    padding: 20px; 
                                    background: #f5f5f5;
                                }
                                .card { 
                                    background: white; 
                                    padding: 20px; 
                                    border-radius: 8px; 
                                    margin: 15px 0;
                                    border: 1px solid #ddd;
                                }
                                .info { 
                                    background: #d1ecf1; 
                                    padding: 15px; 
                                    border-left: 4px solid #17a2b8; 
                                    margin: 15px 0; 
                                }
                            </style>
                        </head>
                        <body>
                            <h3>📄 动态内联iframe内容</h3>
                            <div class='card'>
                                这是动态插入到页面中的内联iframe。请选中这段文字来测试划词功能是否正常工作。
                            </div>
                            <div class='info'>
                                <strong>内联测试：</strong>这个iframe是通过JavaScript动态创建并插入到页面中的。选择这里的文字应该能够触发主页面的AdderToolbar显示。
                            </div>
                            <div class='card'>
                                动态内容测试：验证动态创建的iframe能否被MutationObserver正确检测到。这是测试用的第三段内容。
                            </div>
                        </body>
                        </html>
                    "></iframe>
                </div>
            `;
            
            container.innerHTML += iframeHtml;
            log(`内联iframe #${iframeId} 已创建`);
        }

        function createDelayedIframe() {
            log('创建延迟加载iframe...');
            
            const container = document.getElementById('dynamic-content');
            const iframeId = ++iframeCounter;
            
            // 先创建容器
            const placeholder = document.createElement('div');
            placeholder.className = 'iframe-container';
            placeholder.style.marginTop = '20px';
            placeholder.innerHTML = `
                <div class="iframe-header">延迟加载iframe #${iframeId} - 加载中...</div>
                <div style="padding: 20px; text-align: center; background: #f8f9fa;">
                    <p>iframe将在3秒后加载...</p>
                </div>
            `;
            container.appendChild(placeholder);
            
            // 3秒后创建iframe
            setTimeout(() => {
                placeholder.innerHTML = `
                    <div class="iframe-header">延迟加载iframe #${iframeId} - 已加载</div>
                    <iframe id="delayed-iframe-${iframeId}" srcdoc="
                        <!DOCTYPE html>
                        <html>
                        <head>
                            <style>
                                body { 
                                    font-family: Arial, sans-serif; 
                                    padding: 20px; 
                                    background: #fff5f5;
                                }
                                .delayed { 
                                    background: white; 
                                    padding: 20px; 
                                    border-radius: 8px; 
                                    margin: 15px 0;
                                    border: 2px solid #dc3545;
                                }
                                .warning { 
                                    background: #f8d7da; 
                                    padding: 15px; 
                                    border-left: 4px solid #dc3545; 
                                    margin: 15px 0; 
                                }
                            </style>
                        </head>
                        <body>
                            <h3>⏰ 延迟加载iframe内容</h3>
                            <div class='delayed'>
                                这是延迟3秒后才加载的iframe内容。请选中这段文字来测试延迟加载的iframe是否能够被正确处理。
                            </div>
                            <div class='warning'>
                                <strong>延迟测试：</strong>这个iframe模拟了异步加载的情况。选择这里的文字应该能够触发主页面的AdderToolbar，即使是延迟加载的iframe。
                            </div>
                            <div class='delayed'>
                                异步加载测试：验证定时检查机制是否能够处理延迟出现的iframe。这是第三段测试内容。
                            </div>
                        </body>
                        </html>
                    "></iframe>
                `;
                log(`延迟iframe #${iframeId} 加载完成`);
            }, 3000);
            
            log(`延迟iframe #${iframeId} 已创建，将在3秒后加载内容`);
        }

        function closeModal() {
            document.getElementById('modal').style.display = 'none';
            log('弹框已关闭');
        }

        // 点击弹框外部关闭
        window.onclick = function(event) {
            const modal = document.getElementById('modal');
            if (event.target === modal) {
                closeModal();
            }
        }

        // 页面加载完成
        document.addEventListener('DOMContentLoaded', () => {
            log('页面加载完成，可以开始测试');
            log('请先在主页面文本中测试划词功能');
            log('然后点击按钮创建动态iframe进行测试');
        });
    </script>
</body>
</html>
