# 划词功能问题排查指南

## 问题现象
用户反馈划词功能没有出现，选择文字后没有显示AdderToolbar。

## 根本原因分析

### 🔍 发现的问题
经过代码分析，发现了一个关键问题：

**Guest实例（划词功能的核心）只有在用户登录后才会被创建！**

### 📋 问题详情
1. **依赖链路**: `createStrokeService` → `getNotes` → 用户登录状态
2. **调用时机**: `getNotes`函数只在以下情况被调用：
   - 用户登录成功后
   - 用户重新登录时
   - 便签数据变化时

3. **影响范围**: 未登录用户完全无法使用划词功能

## 🛠️ 解决方案

### 1. 修改初始化逻辑
```typescript
// 在 SidePanelWrapper 中添加独立的初始化函数
const initGuestService = () => {
  if (!guestRef.current) {
    console.log("初始化划词服务...");
    guestRef.current = createStrokeService([]);
  }
};

// 在组件挂载时立即初始化，不依赖登录状态
useEffect(() => {
  // 首先初始化划词服务，不依赖于用户登录状态
  initGuestService();
  
  getUserInfo().then((res) => {
    if (res) {
      setIsUserLoggedIn(true);
      getNotes("init");
    } else {
      setIsUserLoggedIn(false);
    }
  });
  // ...
}, []);
```

### 2. 确保功能独立性
- ✅ 划词功能现在在页面加载时就会初始化
- ✅ 不再依赖用户登录状态
- ✅ 未登录用户也能正常使用划词功能
- ✅ 登录后会更新便签列表，但不影响基础功能

## 🧪 测试验证

### 测试文件
- `test-selection.html`: 基础划词功能测试
- `dynamic-iframe-test.html`: 动态iframe测试

### 测试步骤
1. **基础功能测试**:
   ```bash
   # 打开测试页面
   open test-selection.html
   
   # 检查控制台日志
   # 应该看到: "初始化划词服务..."
   ```

2. **选择文字测试**:
   - 在测试页面中选择任意文字
   - 观察是否出现AdderToolbar
   - 检查控制台是否有相关日志

3. **iframe功能测试**:
   ```bash
   # 打开动态iframe测试页面
   open dynamic-iframe-test.html
   
   # 测试各种iframe场景
   ```

## 🔍 调试方法

### 1. 检查插件加载状态
```javascript
// 在控制台执行
document.getElementById('shadow-side-panel')
// 应该返回插件的Shadow DOM元素
```

### 2. 检查Guest实例
```javascript
// 检查是否有Guest实例创建
// 在插件的Shadow DOM中查找相关元素
const shadowRoot = document.getElementById('shadow-side-panel').shadowRoot;
const annotatorEl = shadowRoot.getElementById('annotator-stroke');
console.log('Annotator element:', annotatorEl);
```

### 3. 检查选择监听器
```javascript
// 手动触发选择事件
const testText = document.querySelector('p');
const range = document.createRange();
range.selectNodeContents(testText);
const selection = window.getSelection();
selection.removeAllRanges();
selection.addRange(range);
```

### 4. 查看详细日志
```javascript
// 开启详细日志
localStorage.setItem('debug', 'true');

// 在控制台查看相关日志
// 应该看到iframe检测、脚本注入等日志
```

## 🚨 常见问题

### 1. 插件未加载
**现象**: 页面中找不到`shadow-side-panel`元素
**解决**: 
- 检查插件是否正确安装
- 确认页面URL匹配插件的matches规则
- 重新加载页面

### 2. Guest实例未创建
**现象**: 选择文字后没有任何反应
**解决**: 
- 检查控制台是否有"初始化划词服务..."日志
- 确认修改后的代码已正确编译
- 重新安装插件

### 3. AdderToolbar不显示
**现象**: Guest实例存在但工具栏不显示
**解决**: 
- 检查CSS样式是否被覆盖
- 确认选择的文字长度足够（通常需要>3个字符）
- 检查是否有JavaScript错误

### 4. iframe功能异常
**现象**: 主页面正常但iframe中划词无效
**解决**: 
- 检查iframe是否同源
- 查看控制台的iframe相关日志
- 确认MutationObserver正常工作

## 📊 性能监控

### 关键指标
- Guest实例创建时间
- 选择事件响应时间
- iframe检测成功率
- 内存使用情况

### 监控方法
```javascript
// 性能监控代码
console.time('Guest初始化');
// ... Guest创建代码
console.timeEnd('Guest初始化');

// 内存使用监控
console.log('内存使用:', performance.memory);
```

## 🔄 回归测试

### 测试清单
- [ ] 未登录状态下划词功能正常
- [ ] 登录后划词功能正常
- [ ] 便签列表更新不影响划词功能
- [ ] iframe中划词功能正常
- [ ] 动态创建的iframe功能正常
- [ ] 页面刷新后功能恢复正常
- [ ] 多标签页功能互不干扰

### 自动化测试
```javascript
// 简单的自动化测试脚本
function runTests() {
  const tests = [
    () => document.getElementById('shadow-side-panel') !== null,
    () => {
      // 模拟文字选择
      const range = document.createRange();
      range.selectNodeContents(document.body.firstElementChild);
      window.getSelection().addRange(range);
      return window.getSelection().toString().length > 0;
    }
  ];
  
  tests.forEach((test, index) => {
    console.log(`测试 ${index + 1}:`, test() ? '✅ 通过' : '❌ 失败');
  });
}
```

## 📝 总结

通过将Guest实例的创建从依赖用户登录状态改为在组件挂载时立即初始化，解决了划词功能不显示的根本问题。现在：

1. **即时可用**: 页面加载后立即可用，无需等待登录
2. **功能独立**: 划词功能不再依赖其他业务逻辑
3. **向后兼容**: 登录用户的体验保持不变
4. **调试友好**: 添加了详细的日志和测试页面

这个修改确保了划词功能的基础可用性，同时保持了与现有功能的兼容性。
