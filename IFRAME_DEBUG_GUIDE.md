# iframe划词功能调试指南

## 问题现象
在动态创建的iframe（特别是弹框中的iframe）中选择文字时，AdderToolbar没有出现。

## 调试步骤

### 1. 基础检查

#### 检查插件是否正确加载
```javascript
// 在浏览器控制台执行
document.getElementById('shadow-side-panel')
// 应该返回插件的Shadow DOM元素
```

#### 检查Guest实例是否创建
```javascript
// 查看控制台日志，应该看到：
// "初始化划词服务..."
```

### 2. iframe检测调试

#### 开启详细日志
打开浏览器控制台，应该能看到以下日志：

```
[调试] 定期检查iframe...
[调试] 发现 X 个iframe
[调试] 检查iframe 1: ...
[调试] 处理新iframe 1
[调试] 尝试设置iframe选择监听器: ...
```

#### 手动检查iframe数量
```javascript
// 检查页面中的iframe数量
document.querySelectorAll('iframe').length

// 检查已处理的iframe数量
document.querySelectorAll('iframe[data-sino-iframe-processed]').length

// 检查已注入脚本的iframe数量
document.querySelectorAll('iframe[data-sino-script-injected]').length
```

### 3. iframe脚本注入调试

#### 检查脚本注入状态
在iframe内容加载后，应该在控制台看到：
```
[调试] 开始向iframe注入选择监听脚本
[调试] iframe脚本注入成功
```

#### 检查iframe内部脚本
在iframe中应该看到：
```
[调试] iframe选择监听脚本已加载
[调试] iframe选择监听器已注册
```

### 4. 选择事件调试

#### 主页面消息监听
当在iframe中选择文字时，主页面应该收到：
```
[调试] 主页面收到iframe选择消息: {type: "iframe-text-selected", ...}
[调试] 处理iframe选择事件: {selectedText: "...", rect: {...}, ...}
```

#### iframe内部选择检测
在iframe中选择文字时，应该看到：
```
[调试] iframe中检测到文本选择: ...
[调试] 向主页面发送选择消息: {...}
```

### 5. AdderToolbar显示调试

#### 检查显示逻辑
```
[调试] 准备显示AdderToolbar，位置: {...}
[调试] 智能菜单设置: {...}
[调试] 显示AdderToolbar
```

#### 检查智能菜单设置
```javascript
// 检查智能菜单设置
browser.storage.local.get(['smartMenu']).then(result => {
  console.log('智能菜单设置:', result.smartMenu);
});

// 检查临时禁用列表
const arr = sessionStorage.getItem("temporaryList") ? 
  JSON.parse(sessionStorage.getItem("temporaryList")) : [];
console.log('临时禁用列表:', arr);
```

## 常见问题及解决方案

### 1. iframe未被检测到

**可能原因**:
- iframe是在Shadow DOM中创建的
- iframe是异步加载的
- MutationObserver未正确配置

**解决方案**:
- 检查`_getAllIframes()`方法是否正确搜索Shadow DOM
- 确认定期检查机制正在运行
- 增加检查频率或延长超时时间

### 2. 脚本注入失败

**可能原因**:
- iframe内容未完全加载
- 跨域限制
- 文档状态检查错误

**解决方案**:
```javascript
// 检查iframe文档状态
const iframe = document.querySelector('iframe');
const doc = iframe.contentDocument;
console.log('iframe文档状态:', doc?.readyState);
console.log('iframe URL:', iframe.src || 'srcdoc');
```

### 3. 消息通信失败

**可能原因**:
- postMessage目标错误
- 消息格式不正确
- 事件监听器未正确注册

**解决方案**:
```javascript
// 手动测试消息通信
window.addEventListener('message', (event) => {
  console.log('收到消息:', event.data);
});

// 在iframe中手动发送消息
window.parent.postMessage({
  type: 'iframe-text-selected',
  selectedText: 'test',
  rect: { top: 100, left: 100, width: 50, height: 20 }
}, '*');
```

### 4. AdderToolbar位置错误

**可能原因**:
- 位置计算错误
- 滚动位置未考虑
- iframe位置获取失败

**解决方案**:
```javascript
// 检查位置计算
const iframe = document.querySelector('iframe');
const iframeRect = iframe.getBoundingClientRect();
console.log('iframe位置:', iframeRect);
console.log('页面滚动:', window.scrollX, window.scrollY);
```

## 测试用例

### 1. 基础测试
使用`debug-iframe-simple.html`进行基础功能测试：
- 主页面划词
- 静态iframe划词
- 动态iframe划词
- 弹框iframe划词

### 2. 压力测试
- 创建多个iframe
- 快速创建和销毁iframe
- 在不同类型的iframe中同时选择文字

### 3. 边界测试
- 空iframe
- 跨域iframe
- 嵌套iframe
- 延迟加载的iframe

## 性能监控

### 关键指标
```javascript
// iframe检测性能
console.time('iframe检测');
const iframes = document.querySelectorAll('iframe');
console.timeEnd('iframe检测');

// 脚本注入性能
console.time('脚本注入');
// ... 注入逻辑
console.timeEnd('脚本注入');

// 消息处理性能
console.time('消息处理');
// ... 消息处理逻辑
console.timeEnd('消息处理');
```

### 内存使用
```javascript
// 检查内存使用
if (performance.memory) {
  console.log('内存使用:', {
    used: Math.round(performance.memory.usedJSHeapSize / 1024 / 1024) + 'MB',
    total: Math.round(performance.memory.totalJSHeapSize / 1024 / 1024) + 'MB',
    limit: Math.round(performance.memory.jsHeapSizeLimit / 1024 / 1024) + 'MB'
  });
}
```

## 故障排除清单

- [ ] 插件正确加载
- [ ] Guest实例已创建
- [ ] iframe检测机制运行正常
- [ ] 脚本成功注入到iframe
- [ ] 消息通信正常
- [ ] 智能菜单设置正确
- [ ] AdderToolbar显示逻辑正常
- [ ] 位置计算准确

## 快速修复

如果遇到问题，可以尝试以下快速修复：

1. **重新加载页面**: 清除所有状态，重新初始化
2. **清除存储**: `browser.storage.local.clear()`
3. **重置智能菜单**: 删除智能菜单设置
4. **手动触发检测**: 在控制台执行iframe检测代码
5. **增加调试日志**: 临时添加更多console.log

## 联系支持

如果问题仍然存在，请提供：
- 浏览器版本和类型
- 插件版本
- 完整的控制台日志
- 重现步骤
- 测试页面URL或代码
