<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简单iframe调试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            line-height: 1.6;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
        }
        .section {
            background: #f5f5f5;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
        }
        .button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .button:hover {
            background: #0056b3;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
        }
        .test-text {
            background: white;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
            border-left: 4px solid #007bff;
        }
        iframe {
            width: 100%;
            height: 300px;
            border: 2px solid #ddd;
            border-radius: 8px;
            margin: 10px 0;
        }
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }
        .modal-content {
            background-color: #fefefe;
            margin: 10% auto;
            padding: 20px;
            border: none;
            width: 70%;
            border-radius: 8px;
            position: relative;
        }
        .close {
            color: #aaa;
            float: right;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }
        .close:hover {
            color: black;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>简单iframe划词调试</h1>
        
        <div class="section">
            <h2>主页面测试</h2>
            <div class="test-text">
                这是主页面的文本。请先在这里测试划词功能是否正常工作。选中这段文字应该会显示AdderToolbar。
            </div>
        </div>

        <div class="section">
            <h2>静态iframe测试</h2>
            <iframe id="static-iframe" srcdoc="
                <!DOCTYPE html>
                <html>
                <head>
                    <style>
                        body { 
                            font-family: Arial, sans-serif; 
                            padding: 20px; 
                            line-height: 1.8; 
                            background: #f0f8ff;
                        }
                        .content { 
                            background: white; 
                            padding: 20px; 
                            border-radius: 8px; 
                            margin: 15px 0;
                        }
                    </style>
                </head>
                <body>
                    <h3>静态iframe内容</h3>
                    <div class='content'>
                        这是静态iframe中的文本内容。请选择这段文字来测试划词功能。如果功能正常，应该会在主页面显示AdderToolbar。
                    </div>
                    <div class='content'>
                        这是第二段测试文本。请尝试选择这里的文字，验证iframe中的划词功能是否正常工作。
                    </div>
                </body>
                </html>
            "></iframe>
        </div>

        <div class="section">
            <h2>动态iframe测试</h2>
            <button class="button" onclick="createDynamicIframe()">创建动态iframe</button>
            <button class="button" onclick="createModalIframe()">创建弹框iframe</button>
            <button class="button" onclick="clearLog()">清空日志</button>
            
            <div id="dynamic-container"></div>
        </div>

        <div class="section">
            <h2>调试日志</h2>
            <div id="log" class="log">等待操作...</div>
        </div>
    </div>

    <!-- 弹框模板 -->
    <div id="modal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeModal()">&times;</span>
            <h3>弹框中的iframe</h3>
            <div id="modal-iframe-container"></div>
        </div>
    </div>

    <script>
        let logElement = document.getElementById('log');
        let iframeCounter = 0;

        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            logElement.innerHTML += `[${timestamp}] ${message}<br>`;
            logElement.scrollTop = logElement.scrollHeight;
            console.log(`[调试] ${message}`);
        }

        function clearLog() {
            logElement.innerHTML = '日志已清空<br>';
        }

        // 监听所有消息
        window.addEventListener('message', (event) => {
            log(`收到消息: ${event.data?.type || 'unknown'} - ${JSON.stringify(event.data).substring(0, 100)}...`);
        });

        // 监听选择变化
        document.addEventListener('selectionchange', () => {
            const selection = window.getSelection();
            if (selection && selection.toString().trim()) {
                log(`主页面选择: "${selection.toString().substring(0, 30)}..."`);
            }
        });

        function createDynamicIframe() {
            log('创建动态iframe...');
            
            const container = document.getElementById('dynamic-container');
            const iframeId = ++iframeCounter;
            
            const iframe = document.createElement('iframe');
            iframe.id = `dynamic-iframe-${iframeId}`;
            iframe.srcdoc = `
                <!DOCTYPE html>
                <html>
                <head>
                    <style>
                        body { 
                            font-family: Arial, sans-serif; 
                            padding: 20px; 
                            background: #f5f5f5;
                        }
                        .card { 
                            background: white; 
                            padding: 20px; 
                            border-radius: 8px; 
                            margin: 15px 0;
                        }
                    </style>
                </head>
                <body>
                    <h3>动态iframe #${iframeId}</h3>
                    <div class='card'>
                        这是动态创建的iframe内容。请选中这段文字来测试划词功能。
                    </div>
                    <div class='card'>
                        这是第二段测试内容。选择这里的文字应该能够触发主页面的AdderToolbar显示。
                    </div>
                    <script>
                        console.log('动态iframe #${iframeId} 脚本已加载');
                        
                        document.addEventListener('selectionchange', () => {
                            const selection = document.getSelection();
                            if (selection && selection.toString().trim()) {
                                console.log('iframe内部检测到选择:', selection.toString().substring(0, 30));
                            }
                        });
                    </script>
                </body>
                </html>
            `;
            
            container.appendChild(iframe);
            log(`动态iframe #${iframeId} 已创建`);
        }

        function createModalIframe() {
            log('创建弹框iframe...');
            
            const container = document.getElementById('modal-iframe-container');
            const iframeId = ++iframeCounter;
            
            container.innerHTML = `
                <iframe id="modal-iframe-${iframeId}" srcdoc="
                    <!DOCTYPE html>
                    <html>
                    <head>
                        <style>
                            body { 
                                font-family: Arial, sans-serif; 
                                padding: 20px; 
                                background: #fff5f5;
                            }
                            .modal-content { 
                                background: white; 
                                padding: 20px; 
                                border-radius: 8px; 
                                margin: 15px 0;
                                border: 2px solid #dc3545;
                            }
                        </style>
                    </head>
                    <body>
                        <h3>弹框iframe #${iframeId}</h3>
                        <div class='modal-content'>
                            这是弹框中的iframe内容。请选中这段文字来测试划词功能是否能在弹框中正常工作。
                        </div>
                        <div class='modal-content'>
                            弹框测试：这段文字用于验证弹框中的iframe是否能够被正确检测和处理。
                        </div>
                        <script>
                            console.log('弹框iframe #${iframeId} 脚本已加载');
                            
                            document.addEventListener('selectionchange', () => {
                                const selection = document.getSelection();
                                if (selection && selection.toString().trim()) {
                                    console.log('弹框iframe内部检测到选择:', selection.toString().substring(0, 30));
                                }
                            });
                        </script>
                    </body>
                    </html>
                " style="width: 100%; height: 300px; border: 2px solid #ddd; border-radius: 8px;"></iframe>
            `;
            
            document.getElementById('modal').style.display = 'block';
            log(`弹框iframe #${iframeId} 已创建`);
        }

        function closeModal() {
            document.getElementById('modal').style.display = 'none';
            log('弹框已关闭');
        }

        // 点击弹框外部关闭
        window.onclick = function(event) {
            const modal = document.getElementById('modal');
            if (event.target === modal) {
                closeModal();
            }
        }

        // 页面加载完成
        document.addEventListener('DOMContentLoaded', () => {
            log('页面加载完成');
            log('请先在主页面测试划词功能');
            log('然后测试静态iframe');
            log('最后测试动态创建的iframe');
            
            // 检查插件状态
            setTimeout(() => {
                const sidePanelElement = document.getElementById('shadow-side-panel');
                if (sidePanelElement) {
                    log('✅ 插件已检测到');
                } else {
                    log('❌ 插件未检测到，请确保插件已正确加载');
                }
            }, 1000);
        });
    </script>
</body>
</html>
