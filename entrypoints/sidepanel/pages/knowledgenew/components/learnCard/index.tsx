import React, { useEffect } from "react";
import { But<PERSON>, Checkbox, Flex, Popconfirm, Tag, Tooltip, theme } from "antd";
import { DeleteOutlined, SelectOutlined, VerticalAlignBottomOutlined } from "@ant-design/icons";
import classnames from "classnames";
import "./index.less";
// import IconFont from "@/components/IconFont";
import { knowdgeSVGIcon } from "@/config/menu/knowdge";

type Props = {
  data: {
    id: string;
    title: string;
    ossId: string;
    createBy?: string;
  };
  checked?: boolean;
  currentUserInfo: any; // 当前用户信息
  knowledgeType?: string; // 当前tab栏切换  3企业 2 项目  4 个人
  onCheckChange?: (checked: boolean, value: any, key: string) => void;
  onDelete?: (item: Props["data"], type: number) => void;
  onTransfer?: (item: Props["data"]) => void;
  ondown?: (item: Props["data"]) => void;
};
const { useToken } = theme;
const KnowledgeCard: React.FC<Props> = ({
  data,
  checked = false,
  currentUserInfo,
  knowledgeType,
  onCheckChange,
  onDelete,
  onTransfer,
  ondown,
}) => {
  const { token } = useToken();
  const fetchRequest = useFetchRequest();
  const fileExtensionHandler = (item: any) => {
    if (item.fileType === "pdf") {
      return <span className="extend-icon">{knowdgeSVGIcon.pdf}</span>;
    } else if (item.fileType === "docx" || item.fileType === "doc") {
      return <span className="extend-icon">{knowdgeSVGIcon.word}</span>;
    } else if (item.fileType === "xls" || item.fileType === "xlsx" || item.fileType === "csv") {
      return <span className="extend-icon">{knowdgeSVGIcon.excel}</span>;
    } else if (item.fileType === "txt") {
      return <span className="extend-icon">{knowdgeSVGIcon.txt}</span>;
    } else if (item.fileType === "pptx") {
      return <span className="extend-icon">{knowdgeSVGIcon.ppt}</span>;
    }
    return <span className="extend-icon"></span>;
  };
  const previewUrl = () => {
    fetchRequest({
      api: "onlinePreviewUrl",
      params: data?.id,
      callback: (res) => {
        if (res.code == 200) {
          window.open(res.data, "_blank");
        }
      },
    });
  };
  return (
    <Flex
      className="learn-card"
      vertical
      gap={token.marginXXS}
      onClick={(e) => {
        e.stopPropagation();
        previewUrl();
      }}
    >
      <Flex justify="space-between">
        <Flex align="center" gap={token.marginXXS}>
          {/* <Button
            style={{ background: token.geekblue1 }}
            className={classnames("btn-icon", { "btn-icon-active": true })}
            type="text"
            size="small"
            icon={<IconFont className="icon" type="knowledgeBaseOutlined" fill={token.geekblue6} />}
          /> */}
          {fileExtensionHandler(data)}
        </Flex>
        <Checkbox
          value={data?.id}
          style={{
            opacity: checked ? 1 : 0,
            transition: "opacity 0.2s",
          }}
          className="knowledge-checkbox"
          checked={checked}
          onClick={(e) => e.stopPropagation()}
          onChange={(e) => {
            e.stopPropagation();
            onCheckChange?.(e.target.checked, data, "2");
          }}
        />
      </Flex>

      <Flex style={{ fontWeight: "bold", fontSize: token.fontSize }} vertical>
        <Flex
          className={classnames("knowledge-name", {
            "show-always": knowledgeType == "3" || currentUserInfo?.id != data?.createBy,
          })}
        >
          {data.title}
        </Flex>
        {/* 企业库不可有任何操作 */}

        <Flex className="knowledge-opeate">
          {/* // 是网页知识库 跟个人库创建的知识可以删除  企业库都不可操作*/}
          {knowledgeType != "3" && (
            <>
              {(currentUserInfo?.id == data?.createBy || knowledgeType == "4") && (
                <Popconfirm
                  title="确认要删除该知识吗？"
                  onConfirm={(e) => {
                    e.stopPropagation();
                    onDelete?.(data, 1);
                  }}
                  onCancel={(e) => {
                    e?.stopPropagation(); // ✅ 阻止“取消”按钮的冒泡
                  }}
                  getPopupContainer={(triggerNode) => triggerNode.parentNode as any}
                  okText="删除"
                  cancelText="取消"
                >
                  <Tooltip
                    placement="top"
                    title="删除"
                    getPopupContainer={(triggerNode) => triggerNode.parentNode as any}
                  >
                    <Button
                      icon={<DeleteOutlined className="know-edit-icon" />}
                      type="text"
                      size="small"
                      onClick={(e) => {
                        e.stopPropagation();
                      }}
                    ></Button>
                  </Tooltip>
                </Popconfirm>
              )}
              {knowledgeType == "4" && (
                <Tooltip placement="top" title="转移" getPopupContainer={(node) => node.parentNode as any}>
                  <Button
                    type="text"
                    size="small"
                    icon={<SelectOutlined />}
                    onClick={(e) => {
                      e.stopPropagation();
                      onTransfer?.(data);
                    }}
                  />
                </Tooltip>
              )}
            </>
          )}
          <Tooltip placement="top" title="下载" getPopupContainer={(triggerNode) => triggerNode.parentNode as any}>
            <Button
              icon={<VerticalAlignBottomOutlined className="know-edit-icon" />}
              type="text"
              size="small"
              onClick={(e) => {
                e.stopPropagation();
                ondown?.(data);
              }}
            ></Button>
          </Tooltip>
        </Flex>
      </Flex>
    </Flex>
  );
};

export default KnowledgeCard;
