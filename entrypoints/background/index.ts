import { myNoticeList, noticeSuccessCallBack, scenarioNotify } from "@/api/message";
import { logout } from "@/api/user.ts";
import registerFetchMessageHandler from "@/entrypoints/background/handlers/fetchMessageHandler.ts";
import registerFetchSSEMessageHandler from "@/entrypoints/background/handlers/fetchSSEMessageHandler.ts";
import { handlePublicApi, handlePublicApiSSE, sendMessageToPopup, sendMessageToAllPopup } from "@/utils/messagebus.ts";
import mqttInstance from "@/utils/mqttInstance"; // 引入 MQTT 实例
import { swManager } from "@/utils/serviceWorkerManager";
import { getTenantId, getToken } from "@/utils/auth.ts";

import { getTeamList, addNoteKnowledge } from "@/api/knowdge";
import { addTask, getTaskList, addUrl } from "@/api/taskCollection";
import { getCurrentUserInfo } from "@/api/user";

// 添加全局变量声明
let currentCaptureListener: ((msg: any) => void) | null = null;

/** 初始化插件API行为 */
function initializingCrxBehavior() {
  // 将浏览器插件badge-click的默认行为改为打开侧边栏
  if (browser.action) {
    if (browser["sidePanel"]) {
      browser["sidePanel"].setPanelBehavior({ openPanelOnActionClick: true });
    }
  }
}

/** 注册插件上下文菜单 */
function registerContextMenu() {
  browser.contextMenus.create({
    id: "1",
    title: "点亮AI",
    contexts: ["page", "selection", "image"],
  });
  browser.contextMenus.create({
    id: "4",
    title: "创建普通便签",
    parentId: "1",
    contexts: ["page", "selection"],
  });
  browser.contextMenus.create({
    id: "5",
    title: "关键信息提取",
    parentId: "1",
    contexts: ["page", "selection"],
  });
  browser.contextMenus.create({
    id: "6",
    title: "财务公司ai场景收集",
    parentId: "1",
    contexts: ["page", "selection"],
  });
  browser.contextMenus.create({
    id: "7",
    title: "快速触发采集任务",
    parentId: "1",
    contexts: ["page", "selection"],
  });
  browser.contextMenus.create({
    id: "8",
    title: "快速创建采集任务",
    parentId: "1",
    contexts: ["page", "selection"],
  });
  browser.contextMenus.create({
    id: "9",
    title: "智能营销图",
    parentId: "1",
    contexts: ["image"],
  });
  browser.contextMenus.onClicked.addListener(async (info, tab) => {
    const menuId: number | string = info.menuItemId;
    let notesType = "";
    if (menuId == "4") notesType = "createNote";
    sendMessageToPopup({ type: notesType });
    const token = await getToken();
    const tenantId = await getTenantId();

    if (menuId === "7" && tab?.id) {
      // 快速触发采集任务
      chrome.scripting.executeScript({
        target: { tabId: tab.id },
        args: [token, tenantId], // 传入token和tenantId
        func: (token, tenantId) => {
          const pageText = document.body.innerText || "";
          const pageUrl = window.location.href;

          // 如果已存在先清理
          const oldModal = document.getElementById("quick-trigger-modal");
          if (oldModal) {
            oldModal.remove();
          }

          // 清理可能存在的旧样式
          const oldStyle = document.getElementById("quick-trigger-modal-style");
          if (oldStyle) {
            oldStyle.remove();
          }

          // 插入样式
          const style = document.createElement("style");
          style.id = "quick-trigger-modal-style";
          style.innerHTML = `
          #quick-trigger-modal {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 600px;
            max-height: 80vh;
            background: white;
            z-index: 999999;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.2);
            overflow: hidden;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
          }
          #quick-trigger-modal .modal-header {
            padding: 16px 24px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid #f0f0f0;
          }
          #quick-trigger-modal .modal-title {
            font-size: 18px;
            font-weight: 600;
            color: #262626;
            margin: 0;
          }
          #quick-trigger-modal .modal-close {
            font-size: 18px;
            cursor: pointer;
            color: #8c8c8c;
            padding: 4px;
            border-radius: 4px;
            transition: all 0.3s;
          }
          #quick-trigger-modal .modal-close:hover {
            background: #f5f5f5;
            color: #262626;
          }
          #quick-trigger-modal .modal-body {
            padding: 24px;
            max-height: calc(80vh - 120px);
            overflow-y: auto;
          }
          #quick-trigger-modal .form-item {
            margin-bottom: 24px;
            display: flex;
            align-items: flex-start;
          }
          #quick-trigger-modal .form-label {
            width: 80px;
            line-height: 32px;
            font-weight: 600;
            color: #262626;
            text-align: left;
            flex-shrink: 0;
          }
          #quick-trigger-modal .sino-form-control {
            flex: 1;
            min-width: 0;
            text-align: left;
          }
          
          /* 任务选择器样式，参考知识库样式 */
          #quick-trigger-modal .task-select-container {
            position: relative;
            width: 95%;
          }
          
          #quick-trigger-modal .task-select-input {
            width: 95%;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 6px 11px;
            border: 1px solid #d9d9d9;
            border-radius: 6px;
            background: white;
            cursor: pointer;
            transition: all 0.3s;
            min-height: 32px;
            position: relative;
          }
          
          #quick-trigger-modal .task-select-input:hover {
            border-color: #1890ff;
          }
          
          #quick-trigger-modal .task-placeholder {
            color: #bfbfbf;
            font-size: 14px;
            position: absolute;
            left: 11px;
            top: 50%;
            transform: translateY(-50%);
            pointer-events: none;
          }
          
          #quick-trigger-modal .task-arrow {
            color: #8c8c8c;
            font-size: 12px;
            transition: transform 0.3s;
            margin-left: auto;
          }
          
          #quick-trigger-modal .task-select-dropdown {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: white;
            border: 1px solid #d9d9d9;
            border-radius: 6px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            z-index: 1000;
            max-height: 300px;
            overflow: hidden;
            margin-top: 4px;
          }
          
          #quick-trigger-modal .task-options {
            max-height: 250px;
            overflow-y: auto;
          }
          
          #quick-trigger-modal .task-option {
            padding: 10px 12px;
            cursor: pointer;
            transition: background-color 0.2s;
            border-bottom: 1px solid #f5f5f5;
            font-size: 14px;
            color: #262626;
            position: relative;
          }
          
          #quick-trigger-modal .task-option:hover {
            background-color: #f0f8ff;
          }
          
          #quick-trigger-modal .task-option.selected {
            background-color: #e6f7ff;
            color: #1890ff;
          }
          
          #quick-trigger-modal .task-option:last-child {
            border-bottom: none;
          }
          
          #quick-trigger-modal .task-loading {
            padding: 20px;
            text-align: center;
            color: #8c8c8c;
            font-size: 14px;
          }
          
          #quick-trigger-modal .task-selected-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 6px;
            flex: 1;
            margin-right: 20px;
            min-height: 20px;
          }
          
          #quick-trigger-modal .task-tag {
            display: inline-flex;
            align-items: center;
            background: #e6f7ff;
            border: 1px solid #91d5ff;
            border-radius: 4px;
            padding: 2px 8px;
            font-size: 12px;
            color: #1890ff;
            max-width: 150px;
          }
          
          #quick-trigger-modal .task-tag-text {
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            margin-right: 4px;
          }
          
          #quick-trigger-modal .task-tag-close {
            cursor: pointer;
            font-weight: bold;
            color: #1890ff;
            margin-left: 4px;
            font-size: 14px;
            line-height: 1;
          }
          
          #quick-trigger-modal .task-tag-close:hover {
            color: #40a9ff;
          }
          
          #quick-trigger-modal .modal-footer {
            display: flex;
            justify-content: flex-end;
            gap: 12px;
            padding: 16px 24px;
            border-top: 1px solid #f0f0f0;
          }
          
          #quick-trigger-modal .btn {
            padding:4px 12px;          
            border-radius: 6px;
            font-size: 14px;
            cursor: pointer;
            border: 1px solid #d9d9d9;
            background: white;
            transition: all 0.3s;
          }
          
          #quick-trigger-modal .btn:hover {
            border-color: #40a9ff;
            color: #40a9ff;
          }
          
          #quick-trigger-modal .btn-primary {
            background: #1890ff;
            border-color: #1890ff;
            color: white;
          }
          
          #quick-trigger-modal .btn-primary:hover {
            background: #40a9ff;
            border-color: #40a9ff;
            color: white;
          }
          `;
          document.head.appendChild(style);

          // 构建弹窗
          const modal = document.createElement("div");
          modal.id = "quick-trigger-modal";

          // 弹窗头部
          const modalHeader = document.createElement("div");
          modalHeader.className = "modal-header";
          modalHeader.innerHTML = `
            <h3 class="modal-title">快速触发采集任务</h3>
          `;
          modal.appendChild(modalHeader);

          // 弹窗主体
          const modalBody = document.createElement("div");
          modalBody.className = "modal-body";

          // 任务选择
          const taskSelectItem = document.createElement("div");
          taskSelectItem.className = "form-item";
          taskSelectItem.innerHTML = `
            <div class="form-label">选择任务</div>
            <div class="sino-form-control">
              <div class="task-select-container">
                <div class="task-select-input" id="taskSelectInput">
                  <div class="task-selected-tags" id="taskSelectedTags"></div>
                  <div class="task-placeholder" id="taskPlaceholder">请选择任务</div>
                  <span class="task-arrow">▼</span>
                </div>
                <div class="task-select-dropdown" id="taskSelectDropdown" style="display: none;">
                  <div class="task-options" id="taskOptions">
                    <div class="task-loading">加载中...</div>
                  </div>
                </div>
              </div>
            </div>
          `;
          modalBody.appendChild(taskSelectItem);

          // 弹窗底部按钮
          const modalFooter = document.createElement("div");
          modalFooter.className = "modal-footer";
          modalFooter.innerHTML = `
            <button class="btn" id="cancelBtn">取消</button>
            <button class="btn btn-primary" id="startTaskBtn">开始采集</button>
          `;
          modalBody.appendChild(modalFooter);

          modal.appendChild(modalBody);
          document.body.appendChild(modal);

          // 弹窗创建完成后立即获取任务列表
          fetchTaskList();

          // 获取任务列表
          async function fetchTaskList() {
            try {
              // 通过chrome.runtime.sendMessage调用background script中的getTaskList
              const response = await new Promise((resolve, reject) => {
                chrome.runtime.sendMessage(
                  {
                    type: "fetchTaskList",
                    data: {
                      pageNum: 1,
                      pageSize: 100,
                      taskName: "",
                    },
                  },
                  (result) => {
                    if (chrome.runtime.lastError) {
                      reject(chrome.runtime.lastError);
                    } else {
                      resolve(result);
                    }
                  },
                );
              });

              if (response && (response as any).code === 200) {
                const taskOptions = document.getElementById("taskOptions");
                taskOptions.innerHTML = "";

                // 存储任务数据供后续使用
                (window as any).taskListData = (response as any).data.records;

                (response as any).data.records.forEach((item: any) => {
                  const optionDiv = document.createElement("div");
                  optionDiv.className = "task-option";
                  optionDiv.setAttribute("data-id", item.id);
                  optionDiv.setAttribute("data-name", item.taskName);
                  optionDiv.textContent = item.taskName;

                  // 添加点击事件
                  optionDiv.addEventListener("click", function () {
                    toggleTaskOption(this);
                  });

                  taskOptions.appendChild(optionDiv);
                });
              } else {
                console.error("获取任务列表失败:", (response as any)?.msg || "未知错误");
              }
            } catch (error) {
              console.error("获取任务列表失败:", error);
            }
          }

          // 切换任务选项的选中状态 - 改为单选
          function toggleTaskOption(optionElement: HTMLElement) {
            // 先清除所有其他选项的选中状态
            const allOptions = document.querySelectorAll(".task-option");
            allOptions.forEach((option) => {
              option.classList.remove("selected");
            });

            // 选中当前选项
            optionElement.classList.add("selected");

            updateSelectedTask();
          }

          // 更新选中的任务显示 - 单选模式
          function updateSelectedTask() {
            const selectedOption = document.querySelector(".task-option.selected");
            const selectedTagsContainer = document.getElementById("taskSelectedTags");
            const placeholder = document.getElementById("taskPlaceholder");

            selectedTagsContainer.innerHTML = "";

            if (!selectedOption) {
              placeholder.style.display = "block";
              placeholder.textContent = "请选择任务";
            } else {
              placeholder.style.display = "none";

              const tag = document.createElement("div");
              tag.className = "task-tag";
              tag.innerHTML = `
                <span class="task-tag-text">${selectedOption.textContent}</span>
                <span class="task-tag-close" data-id="${selectedOption.getAttribute("data-id")}">×</span>
              `;
              selectedTagsContainer.appendChild(tag);

              // 添加删除标签的事件
              tag.querySelector(".task-tag-close").addEventListener("click", function () {
                selectedOption.classList.remove("selected");
                updateSelectedTask();
              });
            }

            // 选择任务后关闭下拉框
            const dropdown = document.getElementById("taskSelectDropdown");
            if (dropdown) {
              dropdown.style.display = "none";
              const arrow = document.querySelector(".task-arrow");
              if (arrow) arrow.textContent = "▼";
            }
          }

          // 切换任务下拉框显示状态
          function toggleTaskDropdown() {
            const dropdown = document.getElementById("taskSelectDropdown");
            const arrow = document.querySelector(".task-arrow");
            const isVisible = dropdown.style.display !== "none";

            if (isVisible) {
              // 关闭下拉框
              dropdown.style.display = "none";
              arrow.textContent = "▼";
            } else {
              // 打开下拉框
              dropdown.style.display = "block";
              arrow.textContent = "▲";
            }
          }

          // 开始采集任务
          async function startTask() {
            const selectedOption = document.querySelector(".task-option.selected");

            if (!selectedOption) {
              alert("请选择任务");
              return;
            }

            // 获取选中的任务
            const taskId = selectedOption.getAttribute("data-id");
            const taskName = selectedOption.textContent;

            // 获取任务的 gatherInfo
            const taskListData = (window as any).taskListData;
            const selectedTask = taskListData.find((task: any) => task.id === taskId);
            const gatherInfo = selectedTask?.gatherInfo || "";

            // 关闭当前弹窗
            document.getElementById("quick-trigger-modal")?.remove();

            try {
              const currentUrl = window.location.href;

              // 若无用户数据则主动获取并缓存
              async function ensureCurrentUser() {
                let user = (window as any).currentUserData;
                if (!user && (window as any).chrome?.runtime?.sendMessage) {
                  try {
                    const response = await new Promise((resolve, reject) => {
                      chrome.runtime.sendMessage({ type: "getCurrentUserInfo", data: {} }, (result) => {
                        if (chrome.runtime.lastError) {
                          reject(chrome.runtime.lastError);
                        } else {
                          resolve(result);
                        }
                      });
                    });
                    if (response && (response as any).code === 200) {
                      user = (response as any).data;
                      (window as any).currentUserData = user;
                    }
                  } catch (e) {
                    // 忽略错误，继续使用 unknown
                  }
                }
                return user;
              }

              const currentUser = await ensureCurrentUser();
              const userAccount = currentUser?.nickName || currentUser?.email || "unknown";

              const updateTaskData = {
                infoId: taskId,
                rootUrl: currentUrl,
                automatic: "是",
                allTake: "是",
                account: userAccount, // 使用当前用户信息
              };

              // 显示采集结果弹窗，先显示加载状态
              showCollectionResultModal(taskId, taskName, null);

              // 使用 SSE 接口获取采集数据
              try {
                // 使用 SSE 接口获取采集数据
                const currentUrl = window.location.href;
                const pageText = document.body.innerText || "";
                const pageTitle = document.title || "";
                const apiKey = "app-0X7Bi2s2emtOcduri9o6VR3u";

                // 检查环境变量，如果没有定义则使用默认值
                const apiBase = "https://copilot.sino-bridge.com:90";

                // 确保 API 地址使用 HTTPS
                let secureApiBase = apiBase;
                if (apiBase.startsWith("http://")) {
                  secureApiBase = apiBase.replace("http://", "https://");
                }

                // 构建包含页面信息的 prompt
                const enhancedPrompt = `${gatherInfo}
                  页面信息：
                  标题：${pageTitle}
                  URL：${currentUrl}
                  内容摘要：${pageText.substring(0, 1000)}${pageText.length > 1000 ? "..." : ""}`;

                // 发送请求数据，按照新的 API 格式
                const requestData = {
                  inputs: {
                    prompt: gatherInfo,
                  },
                  query: enhancedPrompt, // 页面网址信息
                  response_mode: "streaming",
                  conversation_id: "",
                  user: "abc-123",
                  files: [],
                };

                // 初始化流式输出
                let accumulatedContent = "";
                let isStreaming = true;

                // 显示开始流式输出的状态
                updateCollectionResultModal("", false, "", false, true);

                // 设置流式数据监听器
                const streamingListener = (message: any) => {
                  if (message.type === "aiStreamingData" && message.taskId === taskId) {
                    if (message.isComplete) {
                      // 流式输出完成
                      isStreaming = false;
                      updateCollectionResultModal(accumulatedContent, false, "", true);

                      // 调用 addNoteKnowledge 接口将内容存入数据库
                      const selectedTask = (window as any).taskListData?.find((task: any) => task.id === taskId);
                      if (selectedTask && selectedTask.knowledgeHouseId) {
                        chrome.runtime.sendMessage(
                          {
                            type: "addNoteKnowledge",
                            data: {
                              libId: selectedTask.knowledgeHouseId,
                              title: `${pageTitle} - ${new Date().getFullYear()}-${String(new Date().getMonth() + 1).padStart(2, "0")}-${String(new Date().getDate()).padStart(2, "0")}-${String(new Date().getHours()).padStart(2, "0")}-${String(new Date().getMinutes()).padStart(2, "0")}`,
                              content: accumulatedContent,
                            },
                          },
                          (response) => {
                            if (response && response.code === 200) {
                              const saveInfo = document.querySelector(
                                "#collection-result-modal .save-info",
                              ) as HTMLElement;
                              if (saveInfo) {
                                saveInfo.textContent = `已存入知识库：${selectedTask.knowledgeHouse || "未知知识库"}`;
                                saveInfo.style.color = "#52c41a";
                              }

                              // 知识库保存成功后，调用updateTask接口更新任务
                              chrome.runtime.sendMessage(
                                {
                                  type: "updateTask",
                                  data: updateTaskData,
                                },
                                (response) => {
                                  if (response && response.code === 200) {
                                    console.log("任务更新成功");
                                  } else {
                                    alert(response?.msg || "任务更新失败");
                                  }
                                },
                              );
                            } else {
                              const saveInfo = document.querySelector(
                                "#collection-result-modal .save-info",
                              ) as HTMLElement;
                              if (saveInfo) {
                                saveInfo.textContent = `存入知识库失败：${response?.msg || "未知错误"}`;
                                saveInfo.style.color = "#ff4d4f";
                              }
                            }
                          },
                        );
                      } else {
                        const saveInfo = document.querySelector("#collection-result-modal .save-info") as HTMLElement;
                        if (saveInfo) {
                          saveInfo.textContent = "分析完成，但未找到关联的知识库";
                          saveInfo.style.color = "#faad14";
                        }
                      }

                      // 移除监听器
                      chrome.runtime.onMessage.removeListener(streamingListener);
                      return;
                    }

                    if (message.data && message.isStreaming) {
                      // 累积内容
                      accumulatedContent += message.data;

                      // 实时更新显示
                      updateCollectionResultModal(accumulatedContent, false, "", false, false, true);
                    }
                  }
                };

                // 添加流式数据监听器
                chrome.runtime.onMessage.addListener(streamingListener);

                // 通过 background script 发送流式请求
                chrome.runtime.sendMessage(
                  {
                    type: "sendAIRequestStreaming",
                    data: {
                      url: `${secureApiBase}/v1/chat-messages`,
                      requestData: requestData,
                      apiKey: apiKey,
                      taskId: taskId, // 传递任务ID用于标识
                    },
                  },
                  (result) => {
                    if (chrome.runtime.lastError) {
                      updateCollectionResultModal("", false, "请求失败，请检查网络连接");
                      chrome.runtime.onMessage.removeListener(streamingListener);
                      return;
                    }

                    if (!result || !result.success) {
                      updateCollectionResultModal("", false, result?.error || "请求失败");
                      chrome.runtime.onMessage.removeListener(streamingListener);
                    }
                  },
                );
              } catch (error) {
                console.error("获取采集数据失败:", error);
                updateCollectionResultModal("", false, "获取数据失败，请重试");
              }
            } catch (error) {
              console.error("任务执行失败:", error);
              alert("任务执行失败，请重试");
            }
          }

          // 简单的 Markdown 渲染函数
          function renderMarkdown(text: string): string {
            if (!text) return "";

            return (
              text
                // 处理标题
                .replace(
                  /^### (.*$)/gim,
                  '<h3 style="margin: 16px 0 8px 0; font-size: 16px; font-weight: 600; color: #262626;">$1</h3>',
                )
                .replace(
                  /^## (.*$)/gim,
                  '<h2 style="margin: 20px 0 12px 0; font-size: 18px; font-weight: 600; color: #262626;">$1</h2>',
                )
                .replace(
                  /^# (.*$)/gim,
                  '<h1 style="margin: 24px 0 16px 0; font-size: 20px; font-weight: 600; color: #262626;">$1</h1>',
                )

                // 处理粗体和斜体
                .replace(/\*\*(.*?)\*\*/g, '<strong style="font-weight: 600;">$1</strong>')
                .replace(/\*(.*?)\*/g, '<em style="font-style: italic;">$1</em>')

                // 处理代码块
                .replace(
                  /```([\s\S]*?)```/g,
                  '<pre style="background: #f6f8fa; padding: 12px; border-radius: 4px; overflow-x: auto; font-family: monospace; font-size: 13px; margin: 12px 0; border: 1px solid #e1e4e8;"><code>$1</code></pre>',
                )
                .replace(
                  /`([^`]+)`/g,
                  '<code style="background: #f6f8fa; padding: 2px 6px; border-radius: 3px; font-family: monospace; font-size: 13px; border: 1px solid #e1e4e8;">$1</code>',
                )

                // 处理列表
                .replace(/^\* (.*$)/gim, '<li style="margin: 4px 0; padding-left: 8px;">• $1</li>')
                .replace(/^- (.*$)/gim, '<li style="margin: 4px 0; padding-left: 8px;">• $1</li>')
                .replace(/^(\d+)\. (.*$)/gim, '<li style="margin: 4px 0; padding-left: 8px;">$1. $2</li>')

                // 处理链接
                .replace(
                  /\[([^\]]+)\]\(([^)]+)\)/g,
                  '<a href="$2" target="_blank" style="color: #1890ff; text-decoration: none;">$1</a>',
                )

                // 处理换行
                .replace(/\n/g, "<br>")

                // 处理段落
                .replace(/^([^<].*)$/gm, '<p style="margin: 8px 0; line-height: 1.6;">$1</p>')
            );
          }

          // 更新采集结果弹窗内容
          function updateCollectionResultModal(
            content: string,
            isLoading: boolean = false,
            errorMsg: string = "",
            isComplete: boolean = false,
            isStreamingStart: boolean = false,
            isStreamingUpdate: boolean = false,
          ) {
            const newsSection = document.querySelector("#collection-result-modal .info-content");
            if (!newsSection) return;

            if (isLoading) {
              newsSection.innerHTML = "<span>正在分析页面内容，请稍候...</span>";
            } else if (errorMsg) {
              newsSection.innerHTML = `<span style="color: #ff4d4f;">${errorMsg}</span>`;
            } else if (isStreamingStart) {
              // 开始流式输出
              newsSection.innerHTML = `
                <div style="background: #f6f8fa; padding: 15px; border-radius: 6px;">
                  <div style="color: #24292e; margin: 0; padding: 0; min-height: 20px;">
                    <span style="color: #8c8c8c; font-style: italic;">等待数据...</span>
                  </div>
                </div>
              `;
            } else if (isComplete) {
              // 分析完成，通过 Markdown 渲染内容
              const renderedContent = renderMarkdown(content);
              newsSection.innerHTML = `
                <div style="background: #f6f8fa; padding: 15px; border-radius: 6px;">
                  <div style="color: #24292e; margin: 0; padding: 0;">${renderedContent}</div>
                </div>
              `;

              // 更新底部信息 - 这里会在存入知识库后被更新
              const saveInfo = document.querySelector("#collection-result-modal .save-info") as HTMLElement;
              if (saveInfo) {
                saveInfo.textContent = "分析完成，正在存入知识库...";
                saveInfo.style.color = "#1890ff";
              }
            } else if (isStreamingUpdate) {
              // 流式数据更新，实时显示，通过 Markdown 渲染
              const renderedContent = renderMarkdown(content);
              newsSection.innerHTML = `
                <div style="background: #f6f8fa; padding: 15px; ">
                  <div style="color: #24292e; margin: 0; padding: 0;">${renderedContent}</div>
                </div>
              `;

              // 更新底部信息
              const saveInfo = document.querySelector("#collection-result-modal .save-info") as HTMLElement;
              if (saveInfo) {
                saveInfo.textContent = "正在分析页面内容...";
                saveInfo.style.color = "#1890ff";
              }
            } else {
              // 默认情况，通过 Markdown 渲染内容
              const renderedContent = renderMarkdown(content);
              newsSection.innerHTML = `
                <div style="background: #f6f8fa; padding: 15px; border-radius: 6px;">
                  <div style="color: #24292e; margin: 0; padding: 0;">${renderedContent}</div>
                </div>
              `;
            }

            // 自动滚动到底部
            setTimeout(() => {
              const modalBody = document.querySelector("#collection-result-modal .modal-body");
              if (modalBody) {
                modalBody.scrollTop = modalBody.scrollHeight;
              }
            }, 100); // 延迟100ms确保DOM更新完成
          }

          // 显示采集结果弹窗
          function showCollectionResultModal(taskId: string, taskName: string, gatherData?: any) {
            // 清理可能存在的旧弹窗
            const oldResultModal = document.getElementById("collection-result-modal");
            if (oldResultModal) {
              oldResultModal.remove();
            }

            // 插入结果弹窗样式
            const resultStyle = document.createElement("style");
            resultStyle.id = "collection-result-modal-style";
            resultStyle.innerHTML = `
            #collection-result-modal {
              position: fixed;
              top: 50%;
              left: 50%;
              transform: translate(-50%, -50%);
              width: 800px;
              max-height: 90vh;
              background: white;
              z-index: 999999;
              border-radius: 10px;
              box-shadow: 0 0 20px rgba(0,0,0,0.2);
              overflow: hidden;
              font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            }
            #collection-result-modal .modal-header {
              padding: 16px 24px;
              display: flex;
              justify-content: center;
              align-items: center;
              border-bottom: 1px solid #f0f0f0;
              position: relative;
            }
            #collection-result-modal .modal-title {
              font-size: 18px;
              font-weight: 600;
              color: #262626;
            }
            #collection-result-modal .modal-subtitle {
              font-size: 14px;
              color: #8c8c8c;
              margin-top: 4px;
            }
            #collection-result-modal .modal-close {
              position: absolute;
              right: 16px;
              top: 16px;
              font-size: 18px;
              cursor: pointer;
              color: #8c8c8c;
              padding: 4px;
              border-radius: 4px;
              transition: all 0.3s;
            }
            #collection-result-modal .modal-close:hover {
              background: #f5f5f5;
              color: #262626;
            }
            #collection-result-modal .modal-body {
              padding:0 24px;
              max-height: calc(90vh - 200px);
              overflow-y: auto;
            }
            #collection-result-modal .section {
              text-align: left;
            }
            #collection-result-modal .section-title {
              font-size: 16px;
              font-weight: 600;
              color: #262626;
              margin-bottom: 12px;
            }
            #collection-result-modal .info-content {
              padding: 12px 0;
            }
            #collection-result-modal .info-content span {
              display: block;
              padding: 6px 0;
              font-size: 14px;
              color: #262626;
              line-height: 1.5;
            }
            #collection-result-modal .modal-footer {
              display: flex;
              flex-direction: column;
              justify-content: center;
              align-items: center;
            }
            #collection-result-modal .save-info {
              font-size: 14px;
              color: #52c41a;
            }
            #collection-result-modal .btn {
              padding:4px 12px;
              border-radius: 6px;
              font-size: 14px;
              cursor: pointer;
              border: 1px solid #d9d9d9;
              background: white;
              transition: all 0.3s;
            }
            #collection-result-modal .btn:hover {
              border-color: #40a9ff;
              color: #40a9ff;
            }
            #collection-result-modal .btn-primary {
              background: #1890ff;
              border-color: #1890ff;
              color: white;
            }
            #collection-result-modal .btn-primary:hover {
              background: #40a9ff;
              border-color: #40a9ff;
              color: white;
            }
            `;
            document.head.appendChild(resultStyle);

            // 构建结果弹窗
            const resultModal = document.createElement("div");
            resultModal.id = "collection-result-modal";

            // 弹窗头部
            const resultHeader = document.createElement("div");
            resultHeader.className = "modal-header";
            resultHeader.innerHTML = `
              <div>
                <h3 class="modal-title">采集网页关键信息任务</h3>
                <div class="modal-subtitle">提取,保存,知识库</div>
              </div>
            `;
            resultModal.appendChild(resultHeader);

            // 弹窗主体
            const resultBody = document.createElement("div");
            resultBody.className = "modal-body";

            // 新闻摘要部分
            const newsSection = document.createElement("div");
            newsSection.className = "section";

            if (gatherData) {
              // 使用接口返回的真实数据
              const newsContent = gatherData.newsSummary.map((item: string) => `<span>${item}</span><br>`).join("");

              newsSection.innerHTML = `
                <div class="info-content">
                  ${newsContent}
                </div>
              `;
            } else {
              // 显示加载状态
              newsSection.innerHTML = `
                <div class="info-content">
                  <span>正在分析页面内容，请稍候...</span>
                </div>
              `;
            }
            resultBody.appendChild(newsSection);

            // 弹窗底部
            const resultFooter = document.createElement("div");
            resultFooter.className = "modal-footer";
            resultFooter.innerHTML = `
              <div class="save-info">正在分析中...</div>
              <button class="btn btn-primary" onclick="document.getElementById('collection-result-modal').remove()" style="margin-top: 12px;margin-bottom: 12px;">关闭</button>
            `;

            // 重要：将弹窗主体添加到弹窗中！
            resultModal.appendChild(resultBody);
            resultModal.appendChild(resultFooter);

            document.body.appendChild(resultModal);
          }

          // 绑定事件到按钮 - 使用一次性事件绑定，避免重复绑定
          const handleClick = function (e: Event) {
            const target = e.target as HTMLElement;
            if (target.id === "cancelBtn") {
              e.preventDefault();
              e.stopPropagation();
              document.getElementById("quick-trigger-modal")?.remove();
            } else if (target.id === "startTaskBtn") {
              e.preventDefault();
              e.stopPropagation();
              startTask();
            } else if (target.id === "taskSelectInput" || target.closest("#taskSelectInput")) {
              e.preventDefault();
              e.stopPropagation();
              toggleTaskDropdown();
            }
          };

          // 检查是否已经绑定过事件监听器
          if (!document.body.hasAttribute("data-quick-trigger-modal-events-bound")) {
            // 移除可能存在的旧事件监听器
            document.removeEventListener("click", handleClick);
            // 添加新的事件监听器
            document.addEventListener("click", handleClick, { once: false, capture: false });

            // 点击外部区域关闭下拉框
            document.addEventListener("click", function (e) {
              const target = e.target as HTMLElement;
              const dropdown = document.getElementById("taskSelectDropdown");
              const input = document.getElementById("taskSelectInput");

              // 只有当点击的是真正的"外部区域"时才关闭下拉框
              // 不包括任务选项、标签、删除按钮等
              if (
                dropdown &&
                input &&
                !input.contains(target) &&
                !dropdown.contains(target) &&
                !target.classList.contains("task-option") &&
                !target.classList.contains("task-tag") &&
                !target.classList.contains("task-tag-close")
              ) {
                dropdown.style.display = "none";
                const arrow = document.querySelector(".task-arrow");
                if (arrow) arrow.textContent = "▼";
              }
            });

            // 标记已绑定事件
            document.body.setAttribute("data-quick-trigger-modal-events-bound", "true");
          }
        },
      });
    } else if (menuId === "8" && tab?.id) {
      // 快速创建采集任务
      chrome.scripting.executeScript({
        target: { tabId: tab.id },
        args: [token, tenantId], // 传入token和tenantId
        func: (token, tenantId) => {
          const pageText = document.body.innerText || "";
          const pageUrl = window.location.href;

          // 如果已存在先清理
          const oldModal = document.getElementById("task-collection-modal");
          if (oldModal) {
            oldModal.remove();
          }

          // 清理可能存在的旧样式
          const oldStyle = document.getElementById("task-collection-modal-style");
          if (oldStyle) {
            oldStyle.remove();
          }

          // 插入样式
          const style = document.createElement("style");
          style.id = "task-collection-modal-style";
          style.innerHTML = `
          #task-collection-modal {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 800px;
            max-height: 90vh;
            background: white;
            z-index: 999999;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.2);
            overflow: hidden;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
          }
          #task-collection-modal .modal-header {
            padding: 16px 24px;
            display: flex;
            justify-content: space-between;
            align-items: center;
          }
          #task-collection-modal .modal-title {
            font-size: 16px;
            font-weight: 600;
            color: #262626;
            margin: 0;
          }
          #task-collection-modal .modal-close {
            font-size: 18px;
            cursor: pointer;
            color: #8c8c8c;
            padding: 4px;
            border-radius: 4px;
            transition: all 0.3s;
          }
          #task-collection-modal .modal-close:hover {
            background: #f5f5f5;
            color: #262626;
          }
          #task-collection-modal .modal-body {
            padding:0 24px 24px;
            max-height: calc(90vh - 120px);
            overflow-y: auto;
          }
          #task-collection-modal .form-item {
            margin-bottom: 24px;
            display: flex;
            align-items: flex-start;
          }
          #task-collection-modal .form-label {
            width: 100px;
            line-height: 32px;
            font-weight: 600;
            color: #262626;
            text-align: left;
            flex-shrink: 0;
          }
          #task-collection-modal .sino-form-control {
            flex: 1 !important;
            min-width: 0 !important;
            text-align: left !important;
          }
          #task-collection-modal .form-input {
            width: 95% !important;
            height: 32px !important;
            padding: 4px 11px !important;
            border: 1px solid #d9d9d9;
            border-radius: 6px !important;
            font-size: 14px !important;
            transition: all 0.3s !important;
          }
          #task-collection-modal .form-input:focus {
            border-color: #1890ff !important;
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2) !important;
            outline: none !important;
          }
          #task-collection-modal .form-textarea {
            width: 95% !important;
            min-height: 80px !important;
            padding: 8px 11px !important;
            border: 1px solid #d9d9d9 !important;
            border-radius: 6px !important;
            font-size: 14px !important;
            resize: vertical !important;
            font-family: inherit !important;
          }
          #task-collection-modal .form-select {
            width: 40% !important;
            min-height: 32px !important;
            padding: 4px 11px !important;
            border: 1px solid #d9d9d9 !important;
            border-radius: 6px !important;
            font-size: 14px !important;
            background: white !important;
          }
          #task-collection-modal .auto-collect-option {
            display: flex !important;
            align-items: center !important;
            gap: 8px !important;
            margin-top: 6px !important;
          }
          #task-collection-modal .auto-collect-option input[type="checkbox"] {
            margin: 0 !important;
            width: 16px !important;
            height: 16px !important;
          }
          #task-collection-modal .auto-collect-option label {
            font-size: 14px !important;
            color: #262626 !important;
            cursor: pointer !important;
          }
          
          /* 知识库下拉框样式 */
          #task-collection-modal .knowledge-select-container {
            position: relative;
            width: 40%;
          }
          
          #task-collection-modal .knowledge-select-input {
            width: 100%;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 6px 11px;
            border: 1px solid #d9d9d9;
            border-radius: 6px;
            background: white;
            cursor: pointer;
            transition: all 0.3s;
            min-height: 32px;
            position: relative;
          }
          
          #task-collection-modal .knowledge-select-input:hover {
            border-color: #1890ff;
          }
          
          #task-collection-modal .knowledge-placeholder {
            color: #bfbfbf;
            font-size: 14px;
            position: absolute;
            left: 11px;
            top: 50%;
            transform: translateY(-50%);
            pointer-events: none;
          }
          
          #task-collection-modal .knowledge-arrow {
            color: #8c8c8c;
            font-size: 12px;
            transition: transform 0.3s;
            margin-left: auto;
          }
          
          #task-collection-modal .knowledge-select-dropdown {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: white;
            border: 1px solid #d9d9d9;
            border-radius: 6px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            z-index: 1000;
            max-height: 300px;
            overflow: hidden;
            margin-top: 4px;
          }
          
          #task-collection-modal .knowledge-options {
            max-height: 250px;
            overflow-y: auto;
          }
          
          #task-collection-modal .knowledge-option {
            padding: 10px 12px;
            cursor: pointer;
            transition: background-color 0.2s;
            border-bottom: 1px solid #f5f5f5;
            font-size: 14px;
            color: #262626;
            position: relative;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
          
          #task-collection-modal .knowledge-option:hover {
            background-color: #f0f8ff;
          }
          
          #task-collection-modal .knowledge-option.selected {
            background-color: #e6f7ff;
            color: #1890ff;
          }
          
          #task-collection-modal .knowledge-option:last-child {
            border-bottom: none;
          }
          
          #task-collection-modal .knowledge-loading {
            padding: 20px;
            text-align: center;
            color: #8c8c8c;
            font-size: 14px;
          }
          
          #task-collection-modal .knowledge-selected-tags {
            display: flex;
            flex-wrap: nowrap;
            gap: 6px;
            flex: 1;
            margin-right: 20px;
            min-height: 20px;
            overflow: hidden;
          }
          
          #task-collection-modal .knowledge-tag {
            display: inline-flex;
            align-items: center;
            font-size: 14px;
            color: #1890ff;
            max-width: 100%;
            overflow: hidden;
          }
          
          #task-collection-modal .knowledge-tag-text {
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            margin-right: 4px;
            flex: 1;
            min-width: 0;
          }
          
          #task-collection-modal .knowledge-tag-close {
            cursor: pointer;
            font-weight: bold;
            color: #1890ff;
            margin-left: 4px;
            font-size: 14px;
            line-height: 1;
          }
          
          #task-collection-modal .knowledge-tag-close:hover {
            color: #40a9ff;
          }
          #task-collection-modal .url-table-container {
            border: 1px solid #d9d9d9;
            border-radius: 6px;
            overflow: hidden;
            background: white;
            margin-top: 8px;
            width: 98%;
            display: flex;
            flex-direction: column;
          }
          #task-collection-modal .url-table {
            width: 100%;
            display: flex;
            flex-direction: column;
          }
          #task-collection-modal .url-table-header {
            display: flex;
            background: #fafafa;
            border-bottom: 1px solid #f0f0f0;
            min-height: 32px;
          }
          #task-collection-modal .url-header-cell {
            flex: 1;
            font-weight: 600;
            color: #262626;
            text-align: center;
            border-right: 1px solid #f0f0f0;
            font-size: 14px;
            display: flex;
            align-items: center;
            justify-content: center;
          }
          #task-collection-modal .url-header-cell:last-child {
            border-right: none;
          }
          #task-collection-modal .url-table-body {
            overflow-y: auto;
            max-height: 140px;
            scrollbar-width: thin;
            scrollbar-color: #d9d9d9 #f5f5f5;
          }
          #task-collection-modal .url-table-body::-webkit-scrollbar {
            width: 6px;
          }
          #task-collection-modal .url-table-body::-webkit-scrollbar-track {
            background: #f5f5f5;
          }
          #task-collection-modal .url-table-body::-webkit-scrollbar-thumb {
            background-color: #d9d9d9;
            border-radius: 6px;
          }
          #task-collection-modal .url-table-body .url-table-row {
            display: flex;
            border-bottom: 1px solid #f0f0f0;
            background: white;
            min-height: 32px;
          }
          #task-collection-modal .url-table-body .url-table-row:last-child {
            border-bottom: none;
          }
          #task-collection-modal .url-table-body .url-table-row:hover {
            background: #fafafa;
          }
          #task-collection-modal .url-cell {
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            border-right: 1px solid #f0f0f0;
          }
          #task-collection-modal .url-cell:last-child {
            border-right: none;
          }
          #task-collection-modal .url-cell .url-input {
            border: none;
            box-shadow: none;
            width: 100%;
            background: transparent;
            font-size: 14px;
          }
          #task-collection-modal .url-cell .url-input:focus {
            border: none;
            box-shadow: none;
            outline: none;
          }
          #task-collection-modal .url-cell input[type="checkbox"] {
            margin: 0;
            width: 16px;
            height: 16px;
          }
          #task-collection-modal .url-cell .url-actions {
            display: flex;
            gap: 8px;
          }
          #task-collection-modal .url-cell .url-actions button {
            background: none;
            border: none;
            color: #1890ff;
            cursor: pointer;
            padding: 0 8px;
            font-size: 14px;
            text-decoration: none;
          }
          #task-collection-modal .url-cell .url-actions button:hover {
            color: #40a9ff;
          }
          #task-collection-modal .url-cell .url-actions .delete-btn {
            color: #ff4d4f;
            background: none;
            border: none;
            cursor: pointer;
            padding: 4px 8px;
            font-size: 14px;
            border-radius: 4px;
            transition: all 0.3s;
          }
          #task-collection-modal .url-cell .url-actions .delete-btn:hover {
            color: #ff7875;
            background: #fff2f0;
          }
          #task-collection-modal .url-table-footer {
            border-top: 1px solid #f0f0f0;
            text-align: center;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
          }
          #task-collection-modal .add-url-btn {
            color: #1890ff;
            font-weight: 600;
            font-size: 14px;
            background: none;
            border: none;
            cursor: pointer;
            padding: 0;
          }
          #task-collection-modal .add-url-btn:hover {
            color: #40a9ff;
          }
          #task-collection-modal .modal-footer {
            display: flex;
            justify-content: flex-end;
            gap: 12px;
          }
          #task-collection-modal .btn {
            padding:4px 12px;           
            border-radius: 6px;
            font-size: 14px;
            cursor: pointer;
            border: 1px solid #d9d9d9;
            background: white;
            transition: all 0.3s;
          }
          #task-collection-modal .btn:hover {
            border-color: #40a9ff;
            color: #40a9ff;
          }
          #task-collection-modal .btn-primary {
            background: #1890ff;
            border-color: #1890ff;
            color: white;
          }
          #task-collection-modal .btn-primary:hover {
            background: #40a9ff;
            border-color: #40a9ff;
            color: white;
          }
          `;
          document.head.appendChild(style);

          // 构建弹窗
          const modal = document.createElement("div");
          modal.id = "task-collection-modal";

          // 弹窗头部
          const modalHeader = document.createElement("div");
          modalHeader.className = "modal-header";
          modalHeader.innerHTML = `
            <h3 class="modal-title" id="modalTitle">新增收集任务</h3>
          `;
          modal.appendChild(modalHeader);

          // 弹窗主体
          const modalBody = document.createElement("div");
          modalBody.className = "modal-body";

          // 任务名称
          const taskNameItem = document.createElement("div");
          taskNameItem.className = "form-item";
          taskNameItem.innerHTML = `
            <div class="form-label">任务名称<span style="color: red;margin-left:4px;">*</span></div>
            <div class="sino-form-control">
              <input type="text" class="form-input" placeholder="请输入任务名称" id="taskName">
            </div>
          `;
          modalBody.appendChild(taskNameItem);

          // 网址列表
          const urlListItem = document.createElement("div");
          urlListItem.className = "form-item";
          urlListItem.innerHTML = `
            <div class="form-label">网址列表</div>
            <div class="sino-form-control">
              <div class="url-table-container">
                <div class="url-table">
                  <div class="url-table-header">
                    <div class="url-header-cell">网址</div>
                    <div class="url-header-cell">是否整站获取</div>
                    <div class="url-header-cell">操作</div>
                  </div>
                  <div class="url-table-body" id="urlListItems">
                    <div class="url-table-row">
                      <div class="url-cell">
                        <input type="text" class="form-input url-input" placeholder="请输入网址" value="${pageUrl}">
                      </div>
                      <div class="url-cell">
                        <input type="checkbox" id="site0">
                      </div>
                      <div class="url-cell">
                        <div class="url-actions">
                          <button class="delete-btn" data-action="delete">删除</button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="url-table-footer">
                  <button class="add-url-btn">+ 新增</button>
                </div>
              </div>
            </div>
          `;
          modalBody.appendChild(urlListItem);

          // 收集信息
          const collectionInfoItem = document.createElement("div");
          collectionInfoItem.className = "form-item";
          collectionInfoItem.innerHTML = `
            <div class="form-label">收集信息<span style="color: red;margin-left:4px;">*</span></div>
            <div class="sino-form-control">
              <textarea class="form-textarea" placeholder="每行一个URL" id="collectionInfo"></textarea>
            </div>
          `;
          modalBody.appendChild(collectionInfoItem);

          // 知识库
          const knowledgeBaseItem = document.createElement("div");
          knowledgeBaseItem.className = "form-item";
          knowledgeBaseItem.innerHTML = `
            <div class="form-label">知识库<span style="color: red;margin-left:4px;">*</span></div>
            <div class="sino-form-control">
              <div class="knowledge-select-container">
                <div class="knowledge-select-input" id="knowledgeSelectInput">
                  <div class="knowledge-selected-tags" id="knowledgeSelectedTags"></div>
                  <div class="knowledge-placeholder" id="knowledgePlaceholder">请选择知识库</div>
                  <span class="knowledge-arrow">▼</span>
                </div>
                <div class="knowledge-select-dropdown" id="knowledgeSelectDropdown" style="display: none;">
                  <div class="knowledge-options" id="knowledgeOptions">
                    <div class="knowledge-loading">加载中...</div>
                  </div>
                </div>
              </div>
            </div>
          `;
          modalBody.appendChild(knowledgeBaseItem);

          // 是否自动采集
          const automaticItem = document.createElement("div");
          automaticItem.className = "form-item";
          automaticItem.innerHTML = `
            <div class="form-label">是否自动采集</div>
            <div class="sino-form-control">
              <div class="auto-collect-option">
                <input type="checkbox" id="automaticCheckbox">
                <label for="automaticCheckbox">启用自动采集</label>
              </div>
            </div>
          `;
          modalBody.appendChild(automaticItem);

          // 弹窗底部按钮
          const modalFooter = document.createElement("div");
          modalFooter.className = "modal-footer";
          modalFooter.innerHTML = `
            <button class="btn" id="cancelBtn">取消</button>
            <button class="btn btn-primary" id="submitBtn">确定</button>
          `;
          modalBody.appendChild(modalFooter);

          modal.appendChild(modalBody);
          document.body.appendChild(modal);

          // 弹窗创建完成后立即获取知识库列表和用户信息
          fetchKnowledgeList();
          fetchCurrentUser();

          // 获取知识库列表
          async function fetchKnowledgeList() {
            try {
              // 通过chrome.runtime.sendMessage调用background getTeamList
              const response = await new Promise((resolve, reject) => {
                chrome.runtime.sendMessage(
                  {
                    type: "getTeamList",
                    data: {},
                  },
                  (result) => {
                    if (chrome.runtime.lastError) {
                      console.error("Chrome runtime error:", chrome.runtime.lastError);
                      reject(chrome.runtime.lastError);
                    } else {
                      resolve(result);
                    }
                  },
                );
              });

              if (response && (response as any).code === 200) {
                const knowledgeOptions = document.getElementById("knowledgeOptions");
                knowledgeOptions.innerHTML = "";

                // 存储知识库数据供后续使用 - 参考 index.tsx 的数据结构
                const knowledgeData = (response as any).data || [];
                (window as any).knowledgeListData = knowledgeData;

                if (knowledgeData.length > 0) {
                  knowledgeData.forEach((item: any, index: number) => {
                    const optionDiv = document.createElement("div");
                    optionDiv.className = "knowledge-option";
                    optionDiv.setAttribute("data-id", item.id);
                    optionDiv.setAttribute("data-name", item.libName || item.name || "未命名知识库");
                    optionDiv.textContent = item.libName || item.name || "未命名知识库";

                    // 添加点击事件
                    optionDiv.addEventListener("click", function (e) {
                      e.preventDefault();
                      e.stopPropagation();
                      toggleKnowledgeOption(this);
                    });

                    knowledgeOptions.appendChild(optionDiv);
                  });
                } else {
                  knowledgeOptions.innerHTML = '<div class="knowledge-loading">暂无知识库数据</div>';
                }
              } else {
                console.error("获取知识库列表失败:", (response as any)?.msg || "未知错误");
                console.error("完整响应:", response);
                const knowledgeOptions = document.getElementById("knowledgeOptions");
                knowledgeOptions.innerHTML = '<div class="knowledge-loading">获取知识库失败</div>';
              }
            } catch (error) {
              console.error("获取知识库列表失败:", error);
              console.error("错误详情:", error.message, error.stack);
              const knowledgeOptions = document.getElementById("knowledgeOptions");
              knowledgeOptions.innerHTML = '<div class="knowledge-loading">获取知识库出错</div>';

              // 如果是认证错误，显示相应提示
              if (error.code === 401) {
                knowledgeOptions.innerHTML = '<div class="knowledge-loading">认证失败，请重新登录</div>';
              } else if (error.code === 500) {
                knowledgeOptions.innerHTML = '<div class="knowledge-loading">服务器错误，请稍后重试</div>';
              }
            }
          }

          // 获取当前用户信息
          async function fetchCurrentUser() {
            try {
              const response = await new Promise((resolve, reject) => {
                chrome.runtime.sendMessage(
                  {
                    type: "getCurrentUserInfo",
                    data: {},
                  },
                  (result) => {
                    if (chrome.runtime.lastError) {
                      reject(chrome.runtime.lastError);
                    } else {
                      resolve(result);
                    }
                  },
                );
              });

              if (response && (response as any).code === 200) {
                const userData = (response as any).data;

                // 存储用户信息供后续使用
                (window as any).currentUserData = userData;
              } else {
                console.error("获取用户信息失败:", (response as any)?.msg || "未知错误");
              }
            } catch (error) {
              console.error("获取用户信息失败:", error);
            }
          }

          // 切换知识库选项的选中状态 - 改为单选
          function toggleKnowledgeOption(optionElement: HTMLElement) {
            // 先清除所有其他选项的选中状态
            const allOptions = document.querySelectorAll(".knowledge-option");
            allOptions.forEach((option) => {
              option.classList.remove("selected");
            });

            // 选中当前选项
            optionElement.classList.add("selected");

            updateSelectedKnowledge();
            // 选择后关闭下拉框
            const dropdown = document.getElementById("knowledgeSelectDropdown");
            if (dropdown) {
              dropdown.style.display = "none";
              const arrow = document.querySelector(".knowledge-arrow");
              if (arrow) arrow.textContent = "▼";
            }
          }

          // 更新选中的知识库显示 - 单选模式
          function updateSelectedKnowledge() {
            const selectedOption = document.querySelector(".knowledge-option.selected");
            const selectedTagsContainer = document.getElementById("knowledgeSelectedTags");
            const placeholder = document.getElementById("knowledgePlaceholder");

            selectedTagsContainer.innerHTML = "";

            if (!selectedOption) {
              placeholder.style.display = "block";
              placeholder.textContent = "请选择知识库";
            } else {
              placeholder.style.display = "none";

              const tag = document.createElement("div");
              tag.className = "knowledge-tag";
              tag.innerHTML = `
                <span class="knowledge-tag-text">${selectedOption.textContent}</span>
                <span class="knowledge-tag-close" data-id="${selectedOption.getAttribute("data-id")}">×</span>
              `;
              selectedTagsContainer.appendChild(tag);

              // 添加删除标签的事件
              tag.querySelector(".knowledge-tag-close").addEventListener("click", function (e) {
                e.preventDefault();
                e.stopPropagation();
                selectedOption.classList.remove("selected");
                updateSelectedKnowledge();
              });
            }
          }

          // 切换知识库下拉框显示状态
          function toggleKnowledgeDropdown() {
            const dropdown = document.getElementById("knowledgeSelectDropdown");
            const arrow = document.querySelector(".knowledge-arrow");
            const isVisible = dropdown.style.display !== "none";

            if (isVisible) {
              // 关闭下拉框
              dropdown.style.display = "none";
              arrow.textContent = "▼";
            } else {
              // 打开下拉框
              dropdown.style.display = "block";
              arrow.textContent = "▲";
            }
          }

          // 提交任务到后端
          async function submitTaskToBackend(taskData: any) {
            try {
              // 通过chrome.runtime.sendMessage调用background script中的addTask
              const response = await new Promise((resolve, reject) => {
                chrome.runtime.sendMessage(
                  {
                    type: "addTask",
                    data: taskData,
                  },
                  (result) => {
                    if (chrome.runtime.lastError) {
                      reject(chrome.runtime.lastError);
                    } else {
                      resolve(result);
                    }
                  },
                );
              });

              if (response && (response as any).code === 200) {
                alert("任务创建成功！");
                document.getElementById("task-collection-modal")?.remove();
              } else {
                alert(`创建失败：${(response as any)?.msg || "未知错误"}`);
              }
            } catch (error) {
              console.error("提交任务失败:", error);
              alert("提交任务时发生错误，请重试");
            }
          }

          // 定义函数并直接绑定到按钮上
          function addUrl() {
            const urlListItems = document.getElementById("urlListItems");
            const urlCount = urlListItems.children.length;
            const newUrlItem = document.createElement("div");
            newUrlItem.className = "url-table-row";
            newUrlItem.innerHTML = `
              <div class="url-cell">
                <input type="text" class="form-input url-input" placeholder="请输入网址">
              </div>
              <div class="url-cell">
                <input type="checkbox" id="site${urlCount}">
              </div>
              <div class="url-cell">
                <div class="url-actions">
                  <button class="delete-btn" data-action="delete">删除</button>
                </div>
              </div>
            `;
            urlListItems.appendChild(newUrlItem);
            // 添加完新行后滚动到底部
            urlListItems.scrollTo({
              top: urlListItems.scrollHeight,
              behavior: "smooth",
            });
          }

          function deleteUrl(button) {
            const urlListItems = document.getElementById("urlListItems");
            if (urlListItems.children.length > 1) {
              button.closest(".url-table-row").remove();
            }
          }

          function submitTask() {
            const taskName = document.getElementById("taskName") as HTMLInputElement;
            const collectionInfo = document.getElementById("collectionInfo") as HTMLTextAreaElement;
            const automaticCheckbox = document.getElementById("automaticCheckbox") as HTMLInputElement;

            const taskNameValue = taskName && typeof taskName.value === "string" ? taskName.value.trim() : "";
            if (!taskName || taskNameValue.length === 0) {
              alert("请输入任务名称");
              return;
            }

            const collectionInfoValue =
              collectionInfo && typeof collectionInfo.value === "string" ? collectionInfo.value.trim() : "";
            if (!collectionInfo || collectionInfoValue.length === 0) {
              alert("请输入收集信息");
              return;
            }

            // 获取选中的知识库 - 单选模式
            const selectedOption = document.querySelector(".knowledge-option.selected");
            if (!selectedOption) {
              alert("请选择知识库");
              return;
            }

            // 收集网址列表数据
            const urlList = [];
            const urlItems = document.querySelectorAll(".url-table-row");

            // 获取当前用户信息
            const currentUser = (window as any).currentUserData;
            const userAccount = currentUser?.nickName || currentUser?.email || "unknown";

            urlItems.forEach((item, index) => {
              const urlInput = item.querySelector(".url-input") as HTMLInputElement;
              const checkbox = item.querySelector('input[type="checkbox"]') as HTMLInputElement;
              if (urlInput && urlInput.value) {
                urlList.push({
                  rootUrl: urlInput.value,
                  automatic: automaticCheckbox.checked ? "是" : "否",
                  allTake: checkbox ? (checkbox.checked ? "是" : "否") : "否",
                  account: userAccount, // 使用当前用户信息
                });
              }
            });

            if (urlList.length === 0) {
              alert("请至少添加一个有效的网址");
              return;
            }

            // 获取知识库名称和ID - 单选模式
            const knowledgeNames = selectedOption.textContent || "";
            const knowledgeHouseId = selectedOption.getAttribute("data-id") || "";

            const taskData = {
              taskName: taskNameValue,
              knowledgeHouse: knowledgeNames,
              knowledgeHouseId: knowledgeHouseId,
              gatherInfo: collectionInfoValue,
              autoExecute: automaticCheckbox.checked ? "是" : "否", // 根据复选框状态决定是否自动执行
              urlList: urlList,
            };

            // 调用后端接口
            submitTaskToBackend(taskData);
          }

          // 绑定事件到按钮 - 使用一次性事件绑定，避免重复绑定
          const handleClick = function (e: Event) {
            const target = e.target as HTMLElement;
            if (target.classList.contains("add-url-btn")) {
              e.preventDefault();
              e.stopPropagation();
              addUrl();
            } else if (target.id === "cancelBtn") {
              e.preventDefault();
              e.stopPropagation();
              document.getElementById("task-collection-modal")?.remove();
            } else if (target.id === "submitBtn") {
              e.preventDefault();
              e.stopPropagation();
              submitTask();
            } else if (target.classList.contains("delete-btn")) {
              e.preventDefault();
              e.stopPropagation();
              deleteUrl(target);
            } else if (target.id === "knowledgeSelectInput" || target.closest("#knowledgeSelectInput")) {
              e.preventDefault();
              e.stopPropagation();
              toggleKnowledgeDropdown();
            }
          };

          // 检查是否已经绑定过事件监听器
          if (!document.body.hasAttribute("data-task-modal-events-bound")) {
            // 移除可能存在的旧事件监听器
            document.removeEventListener("click", handleClick);
            // 添加新的事件监听器
            document.addEventListener("click", handleClick, { once: false, capture: false });

            // 知识库搜索功能已移除，改为简单点击选择

            // 点击外部区域关闭下拉框
            document.addEventListener("click", function (e) {
              const target = e.target as HTMLElement;
              const dropdown = document.getElementById("knowledgeSelectDropdown");
              const input = document.getElementById("knowledgeSelectInput");

              // 只有当点击的是真正的"外部区域"时才关闭下拉框
              // 不包括知识库选项、标签、删除按钮等
              if (
                dropdown &&
                input &&
                !input.contains(target) &&
                !dropdown.contains(target) &&
                !target.classList.contains("knowledge-option") &&
                !target.classList.contains("knowledge-tag") &&
                !target.classList.contains("knowledge-tag-close")
              ) {
                dropdown.style.display = "none";
                const arrow = document.querySelector(".knowledge-arrow");
                if (arrow) arrow.textContent = "▼";
              }
            });

            // 标记已绑定事件
            document.body.setAttribute("data-task-modal-events-bound", "true");
          }
        },
      });
    } else if ((menuId === "5" || menuId === "6") && tab?.id) {
      // 关键信息提取： /keyInfomation-extract  财务公司ai场景收集： /financial-collection
      let url = menuId === "5" ? "keyInfomation-extract" : "financial-collection";
      let iframeUrl = `${import.meta.env["VITE_TOOLBOX_URL"]}/#/${url}?tenantid=${tenantId}&token=${token}&agentId=${tenantId}`;
      chrome.scripting.executeScript({
        target: { tabId: tab.id },
        args: [iframeUrl], // 传入动态 iframe src
        func: (iframeUrl) => {
          const pageText = document.body.innerText || "";

          // 如果已存在先清理
          const oldModal = document.getElementById("my-custom-modal");
          if (oldModal) oldModal.remove();

          // 插入样式
          const style = document.createElement("style");
          style.id = "custom-modal-style";
          style.innerHTML = `
          #my-custom-modal {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 80%;
            height: 80%;
            background: white;
            z-index: 999999;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.2);
            overflow: hidden;
          }
          #my-custom-modal iframe {
            width: 100%;
            height: 100%;
            border: none;
          }
          #my-modal-close {
            position: absolute;
            top: 8px;
            right: 8px;
            z-index: 1000000;
            font-size: 18px;
            cursor: pointer;
          }
        `;
          document.head.appendChild(style);

          // 构建 modal
          const modal = document.createElement("div");
          modal.id = "my-custom-modal";

          const closeBtn = document.createElement("div");
          closeBtn.id = "my-modal-close";
          closeBtn.innerHTML = `<svg
            viewBox="64 64 896 896"
            focusable="false"
            data-icon="close"
            width="22"
            height="22"
            fill="currentColor"
            aria-hidden="true"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path d="M563.8 512l182.1-182.1a7.92 7.92 0 000-11.3l-48.4-48.4a7.92
              7.92 0 00-11.3 0L504 452.2 321.9 270.1a7.92 7.92 0 00-11.3 0l-48.4
              48.4a7.92 7.92 0 000 11.3L452.2 512 270.1 694.1a7.92 7.92 0 000
              11.3l48.4 48.4a7.92 7.92 0 0011.3 0L504 571.8l182.1
              182.1a7.92 7.92 0 0011.3 0l48.4-48.4a7.92 7.92 0 000-11.3L563.8 512z"/>
          </svg>`;
          closeBtn.onclick = () => {
            modal.remove();
          };
          modal.appendChild(closeBtn);

          const iframe = document.createElement("iframe");
          iframe.src = iframeUrl;
          modal.appendChild(iframe);

          document.body.appendChild(modal);

          // 向 iframe 发送页面内容
          // 等子组件准备好了之后，开始发送
          window.addEventListener("message", (event) => {
            if (event.data?.type === "ready") {
              iframe.contentWindow?.postMessage(
                {
                  type: "pageText",
                  text: pageText,
                  url: window.location.href,
                },
                "*",
              );
            }
          });
        },
      });
    } else if (menuId == "9") {
      const token = await getToken();
      const tenantId = await getTenantId();
      const refreshToken = await getRefereshToken();
      console.log(info.srcUrl, 11221);
      // let iframeUrl = `http://localhost:5173/copilot/login/#?pathName=webImg&tenantId=${tenantId}&token=${token}&agentId=${tenantId}&refreshToken=${refreshToken}`;
      let iframeUrl = `${import.meta.env["VITE_COPILOT_URL"]}/login/#?pathName=webImg&tenantId=${tenantId}&token=${token}&agentId=${tenantId}&refreshToken=${refreshToken}`;
      console.log(iframeUrl, 122);
      chrome.scripting.executeScript({
        target: { tabId: tab.id },
        args: [iframeUrl, info.srcUrl], // 传入动态 iframe src
        func: (iframeUrl, srcUrl) => {
          // 如果已存在先清理
          const oldModal = document.getElementById("my-custom-modal");
          if (oldModal) oldModal.remove();

          // 插入样式
          const style = document.createElement("style");
          style.id = "custom-modal-style";
          style.innerHTML = `
          #my-custom-modal {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 80%;
            height: 80%;
            background: white;
            z-index: 999999;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.2);
            overflow: hidden;
          }
          #my-custom-modal iframe {
            width: 100%;
            height: 100%;
            border: none;
          }
          #my-modal-close {
            position: absolute;
            top: 8px;
            right: 8px;
            z-index: 1000000;
            font-size: 18px;
            cursor: pointer;
          }
        `;
          document.head.appendChild(style);

          // 构建 modal
          const modal = document.createElement("div");
          modal.id = "my-custom-modal";

          const closeBtn = document.createElement("div");
          closeBtn.id = "my-modal-close";
          closeBtn.innerHTML = `<svg
            viewBox="64 64 896 896"
            focusable="false"
            data-icon="close"
            width="22"
            height="22"
            fill="currentColor"
            aria-hidden="true"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path d="M563.8 512l182.1-182.1a7.92 7.92 0 000-11.3l-48.4-48.4a7.92
              7.92 0 00-11.3 0L504 452.2 321.9 270.1a7.92 7.92 0 00-11.3 0l-48.4
              48.4a7.92 7.92 0 000 11.3L452.2 512 270.1 694.1a7.92 7.92 0 000
              11.3l48.4 48.4a7.92 7.92 0 0011.3 0L504 571.8l182.1
              182.1a7.92 7.92 0 0011.3 0l48.4-48.4a7.92 7.92 0 000-11.3L563.8 512z"/>
          </svg>`;
          closeBtn.onclick = () => {
            modal.remove();
          };
          modal.appendChild(closeBtn);

          const iframe = document.createElement("iframe");
          iframe.src = iframeUrl;
          modal.appendChild(iframe);

          document.body.appendChild(modal);
          // 向 iframe 发送页面内容
          // 等子组件准备好了之后，开始发送
          console.log(srcUrl, 332); // ✅ 可以访问
          window.addEventListener("message", (event) => {
            if (event.data?.type === "ready") {
              iframe.contentWindow?.postMessage(
                {
                  type: "pageText",
                  text: srcUrl,
                  url: window.location.href,
                },
                "*",
              );
            }
          });
        },
      });
    }
  });
}

// eslint-disable-next-line require-jsdoc
function initializeAlarm() {
  chrome.cookies.onChanged.addListener(({ cookie, removed }) => {
    if ("scrm.sino-bridge.com" == cookie.domain && cookie.name == "userInfo") {
      if (removed) {
        // 登出
        logout().then(() => {
          browser.storage.local.clear();
        });
      } else {
        // 登录
        cookie.value &&
          browser.storage.local.set({
            userInfo: cookie.value,
          });
      }
    }
  });
}

let notificationsList = [];
// 获取未发送通知的列表，并回调
browser.alarms.onAlarm.addListener(function (alarm) {
  if (alarm.name === "sendChromeMessage") {
    myNoticeList({ readFlag: "read_flag_false", noticeFlag: "notice_flag_false" })
      .then((res) => {
        if (res.code == 401) {
          browser.alarms.clear("sendChromeMessage");
          return;
        }
        if (res.data && res.data.length > 0) {
          notificationsList = res.data;
          res.data.forEach((item) => {
            const id = `${item.noticeRecId}-${item.busiType}`;
            browser.notifications
              .create(id, {
                type: "basic",
                iconUrl: browser.runtime.getURL("/images/logo.png"), // 通知图标路径
                title: item.msgTitle,
                message: item.busiType === "busi_type_version" ? "点击去下载" : "有人@你，赶紧看看",
              })
              .then((notificationId) => {
                let id = notificationId.split("-")[0];
                noticeSuccessCallBack([id]).then((res) => {
                  console.debug("用户已经收到通知了");
                });
              })
              .catch((error) => {
                console.error("通知发送失败:", error);
              });
            setTimeout(() => {
              browser.notifications.clear(id);
            }, 5000);
          });
        }
      })
      .catch((error) => {
        if (error.code == 401) {
          browser.alarms.clear("sendChromeMessage");
          return;
        }
      });
    scenarioNotify({})
      .then((res) => {
        if (res.code == 401) {
          browser.alarms.clear("sendChromeMessage");
          return;
        }
        if (res.code == "200" && res.data && res.data.length > 0) {
          notificationsList = res.data;
          res.data.forEach((item) => {
            const id = `${item.id}-${item.createBy}`;
            browser.notifications
              .create(id, {
                type: "basic",
                iconUrl: browser.runtime.getURL("/images/logo.png"), // 通知图标路径
                title: item.msgTitle,
                message: "有人@你，赶紧看看",
              })
              .then((notificationId) => {
                console.log(notificationId);
              })
              .catch((error) => {
                console.error("通知发送失败:", error);
              });
            setTimeout(() => {
              browser.notifications.clear(id);
            }, 5000);
          });
        }
      })
      .catch((error) => {
        if (error.code == 401) {
          browser.alarms.clear("sendChromeMessage");
          return;
        }
      });
  }
});
function createTabs() {
  // 使用状态管理器检查是否已注册标签页监听器
  if (swManager.isInitialized("tabsListenerRegistered")) {
    console.debug("标签页监听器已经注册，跳过重复注册");
    return;
  }

  // 使用安全初始化方法注册标签页监听器
  swManager.safeInit("tabsListenerRegistered", () => {
    chrome.tabs.onCreated.addListener((tab) => {
      // 排除不需要的标签页
      if (!["设置 - AI办公助手", "扩展程序", "新标签页"].includes(tab.title)) {
        // 监听新创建的标签页的加载状态
        const tabLoadListener = (tabId: number, changeInfo: chrome.tabs.TabChangeInfo, updatedTab: chrome.tabs.Tab) => {
          // 只处理我们关注的那个标签页
          if (tabId === tab.id && changeInfo.status === "complete") {
            // 通知所有页面有新标签页创建
            setTimeout(() => {
              sendMessageToAllPopup({
                type: "tabCreated",
                tab: {
                  id: updatedTab.id,
                  title: updatedTab.title,
                  url: updatedTab.url,
                  favIconUrl: updatedTab.favIconUrl || browser.runtime.getURL("/images/ico.png"),
                },
              });
            }, 3000);
            // 移除监听器，避免重复处理
            chrome.tabs.onUpdated.removeListener(tabLoadListener);
          }
        };
        // 添加标签页更新监听器
        chrome.tabs.onUpdated.addListener(tabLoadListener);
      }
    });
  });
}

// 消息通知点击事件
browser.notifications.onClicked.addListener((notificationId) => {
  const type = notificationId.split("-")[1];
  if (type === "busi_type_version") {
    const OFFICIAL_URL = import.meta.env["VITE_OFFICIAL_URL"];
    chrome.tabs.create({ url: `${OFFICIAL_URL}/version` });
    return;
  }
  browser.tabs.create({
    url: browser.runtime.getURL("/options.html?message=3"),
  });
  // browser.runtime.sendMessage({ type: "updateOptions"});

  // window.open(browser.runtime.getURL("/options.html?note=1"), "_blank");
  // chrome.runtime.sendMessage({ action: "getTabs" }, (response) => {
  //   if (response) {
  //     // 显示所有标签页的信息
  //     let num = 0;
  //     response.forEach((tab) => {
  //       if (tab.url.includes("chrome-extension://") && tab.url.includes("options.html")) {
  //         num++;
  //       }
  //     });
  //     window.open(browser.runtime.getURL("/options.html?note=1"), "_blank");
  //   }
  // });
});
// 监听缓存看用户信息是否有更新 并创建定时器
browser.storage.onChanged.addListener((changes) => {
  if (Object.prototype.hasOwnProperty.call(changes, "userInfo")) {
    if (changes.userInfo.newValue) {
      browser.alarms.create("sendChromeMessage", {
        periodInMinutes: 0.3,
      });
    } else {
      browser.alarms.clear("sendChromeMessage");
    }
  }
});

// 转发popup与content.js之间的消息
// eslint-disable-next-line require-jsdoc
function publicMessageListener() {
  // 使用状态管理器检查是否已注册
  if (swManager.isInitialized("messageListenerRegistered")) {
    console.debug("消息监听器已经注册，跳过重复注册");
    return;
  }

  console.debug("开始注册消息监听器...");

  browser.runtime.onMessage.addListener((message: any, sender: any, sendResponse: any) => {
    if (message.fetchType == "fetch" && message.api && message.type == "send") {
      // 调用公共处理接口的方法
      handlePublicApi(message.api, message.params);
    }
    if (message.fetchType == "fetchSSE" && message.url && message.type == "sendSSE") {
      // background调用发送sse请求方法
      handlePublicApiSSE(message.url, message.headers, message.body, message.query, message.instruct);
    }
    // 截图  接受orc发送的开启截图的消息并转发到content.js
    if (message.type === "screenshot") {
      browser.tabs?.query({ active: true, currentWindow: true }).then(async (tabs) => {
        try {
          await browser.scripting.insertCSS({
            target: {
              tabId: tabs[0].id,
            },
            files: ["snipping/inject.css"],
          });
          await browser.scripting.executeScript({
            target: {
              tabId: tabs[0].id,
            },
            files: ["snipping/inject.js"],
          });
        } catch (e) {
          console.error(e);
        }
      });
    } else if (message.type === "captured") {
      const { left, top, width, height } = message;
      if (!width || !height) {
        return;
      }
      browser.tabs
        .captureVisibleTab(sender.tab.windowId, {
          format: "png",
        })
        .then(async (href) => {
          sendMessageToPopup({ type: "captured", href, left, top, width, height });
        });
    } else if (message.type === "captureTab") {
      // 截图当前标签页
      chrome.tabs.query({ active: true, currentWindow: true }, async ([tab]) => {
        if (!tab || !tab.id) {
          sendResponse({ error: "未找到当前标签页" });
          return;
        }
        let responseHandled = false; // 添加标志位追踪是否已经处理过响应

        try {
          // 清理之前的监听器
          if (currentCaptureListener) {
            chrome.runtime.onMessage.removeListener(currentCaptureListener);
            currentCaptureListener = null;
          }

          await browser.scripting.executeScript({
            target: { tabId: tab.id },
            files: ["contentScripts/html2canvas.js"],
          });
          await browser.scripting.executeScript({
            target: { tabId: tab.id },
            files: ["contentScripts/pageCapture.js"],
          });

          // 设置新的监听器并保存引用
          const createOneTimeListener = () => {
            const listener = (msg: any) => {
              if (msg.type === "captureResult") {
                // 立即移除监听器
                chrome.runtime.onMessage.removeListener(listener);
                currentCaptureListener = null;

                if (!responseHandled) {
                  // 确保只响应一次
                  responseHandled = true;
                  if (msg.error) {
                    sendResponse({ error: msg.error });
                  } else {
                    sendResponse({ dataUrl: msg.result });
                  }
                }
                return true;
              }
              return false;
            };
            return listener;
          };

          // 设置新的监听器
          currentCaptureListener = createOneTimeListener();
          chrome.runtime.onMessage.addListener(currentCaptureListener);

          // 触发截图
          await chrome.scripting.executeScript({
            target: { tabId: tab.id },
            func: () => {
              window.postMessage({ type: "triggerCapture" }, "*");
            },
          });

          // 设置超时处理，避免监听器永久存在
          setTimeout(() => {
            if (currentCaptureListener) {
              chrome.runtime.onMessage.removeListener(currentCaptureListener);
              currentCaptureListener = null;
              sendResponse({ error: "截图超时" });
            }
          }, 30000); // 30秒超时
        } catch (e) {
          // 发生错误时也要清理监听器
          if (currentCaptureListener) {
            chrome.runtime.onMessage.removeListener(currentCaptureListener);
            currentCaptureListener = null;
          }
          sendResponse({ error: "截图失败：" + e.message });
        }
      });
      return true;
    } else if (message.type === "mqttAndGoLogin") {
      const { uuid } = message;
      mqttInstance.subscribe(`/mqtt/topic/sino/lamp/oauth/token/login/${uuid}`, (mqMsg) => {
        console.log("接收官网消息-登录=>", `/mqtt/topic/sino/lamp/oauth/token/login/${uuid}`, mqMsg);
        const msg = JSON.parse(mqMsg);
        console.log("接收官网消息-登录消息=1>", msg);
        sendMessageToPopup({ type: "mqttAndGoLogin", refreshToken: msg?.refreshToken, tenantId: msg?.tenantId });
      });
    } else if (message.type === "mqttAndLogout") {
      const { userId } = message;
      mqttInstance.subscribe(`/mqtt/topic/sino/lamp/oauth/token/logout/${userId}`, (mqMsg) => {
        console.log("接收官网消息-退出登录=>", `/mqtt/topic/sino/lamp/oauth/token/logout/${userId}`, mqMsg);
        sendMessageToPopup({ type: "mqttAndLogout" });
      });
    } else if (message.type === "mqttResourceChange") {
      const { userId, tenantId } = message;
      mqttInstance.subscribe(`/mqtt/topic/sino/lamp/oauth/role/update/${userId}/${tenantId}`, (mqMsg) => {
        console.log("接收后台消息-权限变更=>", `/mqtt/topic/sino/lamp/oauth/role/update/${userId}/${tenantId}`, mqMsg);
        sendMessageToPopup({ type: "mqttResourceChange" });
      });
    } else if (message.type === "mqttPointChange") {
      const { userId, tenantId } = message;
      mqttInstance.subscribe(`/mqtt/topic/sino/lamp/point/update/${userId}/${tenantId}`, (mqMsg) => {
        console.log("接收后台消息-积分变化=>", `/mqtt/topic/sino/lamp/point/update/${userId}/${tenantId}`, mqMsg);
        const msg = JSON.parse(mqMsg);
        sendMessageToAllPopup({ type: "mqttPointChange", point: msg?.point, totalPoint: msg?.totalPoint });
      });
    } else if (message.type === "endMqtt") {
      mqttInstance.destroy();
    } else if (message.type === "openChat") {
      sendMessageToPopup(message);
    } else if (message.type === "getDifySuggestions") {
      // 异步执行API调用
      (async () => {
        try {
          const { fieldContext, fieldType, pageContext } = message.payload;
          const apiKey = import.meta.env.VITE_AI_FORM_FILLING;
          const apiUrl = import.meta.env.VITE_AI_API_BASE + "/chat-messages";

          if (!apiKey || !apiUrl) {
            throw new Error("API Key or URL is not configured in .env files.");
          }

          const response = await fetch(apiUrl, {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
              Authorization: `Bearer ${apiKey}`,
            },
            body: JSON.stringify({
              inputs: {
                field_context: fieldContext,
                field_type: fieldType,
                page_context: pageContext,
                suggestion_count: 3, // 暂时硬编码为3，后续可以作为参数传入
              },
              query: "1",
              response_mode: "blocking",
              user: "amateur-form-smartfill-user", // 标识用户
            }),
          });

          if (!response.ok) {
            throw new Error(`API request failed with status ${response.status}`);
          }

          const data = await response.json();

          // 更健壮的JSON提取逻辑，使用括号匹配算法
          const rawAnswer = data.answer;
          const firstBrace = rawAnswer.indexOf("{");
          console.log(firstBrace);
          if (firstBrace === -1) {
            throw new Error("No JSON object found in the API response.");
          }

          let openBraces = 0;
          let jsonEnd = -1;
          for (let i = firstBrace; i < rawAnswer.length; i++) {
            if (rawAnswer[i] === "{") {
              openBraces++;
            } else if (rawAnswer[i] === "}") {
              openBraces--;
            }
            if (openBraces === 0) {
              jsonEnd = i;
              break; // 找到匹配的结束括号
            }
          }

          if (jsonEnd === -1) {
            throw new Error("Could not find the end of the JSON object in the API response.");
          }
          console.log("openBraces", openBraces);
          console.log("jsonEnd", jsonEnd);
          console.log(rawAnswer, "rawAnswer");
          const jsonString = rawAnswer.substring(firstBrace, jsonEnd + 1);
          const suggestions = JSON.parse(jsonString).suggestions;

          sendResponse({ status: "success", suggestions });
        } catch (error) {
          console.error("Dify API call failed:", error);
          const errorMessage = error instanceof Error ? error.message : String(error);
          sendResponse({ status: "error", message: errorMessage });
        }
      })();

      // 返回 true 表示我们将异步地发送响应
      return true;
    } else if (message.type === "fetchKnowledgeTeam") {
      // 处理获取知识库列表的请求
      (async () => {
        try {
          const response = await getTeamList({});
          sendResponse(response);
        } catch (error) {
          console.error("获取知识库列表失败:", error);
          sendResponse({ code: 500, msg: "获取知识库列表失败" });
        }
      })();
      return true; // 保持消息通道开放
    } else if (message.type === "addTask") {
      // 处理添加任务的请求
      (async () => {
        try {
          const response = await addTask(message.data);
          sendResponse(response);
        } catch (error) {
          console.error("添加任务失败:", error);
          sendResponse({ code: 500, msg: "添加任务失败" });
        }
      })();
      return true; // 保持消息通道开放
    } else if (message.type === "updateTask") {
      // 处理更新任务的请求
      (async () => {
        try {
          const response = await addUrl(message.data);
          sendResponse(response);
        } catch (error) {
          console.error("更新任务失败:", error);
          sendResponse({ code: 500, msg: "更新任务失败" });
        }
      })();
      return true; // 保持消息通道开放
    } else if (message.type === "fetchTaskList") {
      // 处理获取任务列表的请求
      (async () => {
        try {
          const response = await getTaskList(message.data);
          sendResponse(response);
        } catch (error) {
          console.error("获取任务列表失败:", error);
          sendResponse({ code: 500, msg: "获取任务列表失败" });
        }
      })();
      return true; // 保持消息通道开放
    } else if (message.type === "getTeamList") {
      // 处理获取知识库列表的请求
      (async () => {
        try {
          // 获取认证信息
          const token = await getToken();
          const tenantId = await getTenantId();

          if (!token || !tenantId) {
            console.error("缺少认证信息");
            sendResponse({ code: 401, msg: "缺少认证信息" });
            return;
          }

          const response = await getTeamList({});

          sendResponse(response);
        } catch (error) {
          console.error("获取知识库列表失败:", error);
          console.error("错误详情:", error.message, error.stack);
          sendResponse({ code: 500, msg: "获取知识库列表失败" });
        }
      })();
      return true; // 保持消息通道开放
    } else if (message.type === "getCurrentUserInfo") {
      // 处理获取当前用户信息的请求
      (async () => {
        try {
          // 获取认证信息
          const token = await getToken();
          const tenantId = await getTenantId();

          if (!token || !tenantId) {
            console.error("缺少认证信息");
            sendResponse({ code: 401, msg: "缺少认证信息" });
            return;
          }

          // 这里需要导入 getCurrentUserInfo 函数，暂时使用模拟数据
          const response = await getCurrentUserInfo({});
          sendResponse(response);
        } catch (error) {
          console.error("获取用户信息失败:", error);
          sendResponse({ code: 500, msg: "获取用户信息失败" });
        }
      })();
      return true; // 保持消息通道开放
    } else if (message.type === "addNoteKnowledge") {
      // 处理添加笔记到知识库的请求
      (async () => {
        try {
          const { libId, title, content } = message.data;

          const response = await addNoteKnowledge({
            libId,
            title,
            content,
          });

          sendResponse(response);
        } catch (error) {
          sendResponse({
            code: 500,
            msg: error.message || "添加笔记到知识库失败",
          });
        }
      })();
      return true; // 保持消息通道开放
    } else if (message.type === "sendAIRequest") {
      // 处理发送 AI 请求的请求
      (async () => {
        try {
          const { url, requestData, apiKey } = message.data;

          const response = await fetch(url, {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
              Authorization: `Bearer ${apiKey}`,
            },
            body: JSON.stringify(requestData),
          });

          if (!response.ok) {
            const errorText = await response.text();
            throw new Error(`HTTP error! status: ${response.status}, response: ${errorText}`);
          }

          // 检查是否是流式响应
          const contentType = response.headers.get("content-type");

          if (contentType && contentType.includes("text/event-stream")) {
            // 流式响应处理
            const reader = response.body?.getReader();
            if (!reader) {
              throw new Error("无法获取响应流");
            }

            // 开始读取流式数据
            let accumulatedAnswer = "";
            let allEvents: any[] = [];
            let timeoutId: NodeJS.Timeout | null = null;

            try {
              let isReading = true;
              let hasData = false;

              // 添加超时保护
              timeoutId = setTimeout(() => {
                if (allEvents.length > 0) {
                  sendResponse({
                    success: true,
                    data: allEvents,
                  });
                } else {
                  sendResponse({
                    success: false,
                    error: "流式响应处理超时",
                  });
                }
              }, 15000); // 15秒超时

              while (isReading) {
                const { done, value } = await reader.read();

                if (done) {
                  isReading = false;
                  break;
                }

                const chunk = new TextDecoder().decode(value);
                const lines = chunk.split("\n");

                for (const line of lines) {
                  if (line.startsWith("data: ")) {
                    const data = line.slice(6);
                    hasData = true;

                    if (data === "[DONE]") {
                      // 数据接收完成
                      if (timeoutId) clearTimeout(timeoutId);
                      sendResponse({
                        success: true,
                        data: allEvents, // 返回所有事件数据，让前端处理
                      });
                      return;
                    }

                    try {
                      const parsed = JSON.parse(data);
                      allEvents.push(parsed);

                      // 当 event 是 message 时，拼接 answer 字段
                      if (parsed.event === "message" && parsed.answer) {
                        accumulatedAnswer += parsed.answer;
                      }
                    } catch (e) {
                      // 忽略解析错误
                    }
                  }
                }
              }

              // 如果没有收到 [DONE] 信号但有数据，也要返回
              if (hasData && allEvents.length > 0) {
                if (timeoutId) clearTimeout(timeoutId);
                sendResponse({
                  success: true,
                  data: allEvents,
                });
                return;
              }

              // 清理超时定时器
              if (timeoutId) clearTimeout(timeoutId);
            } catch (error) {
              // 清理超时定时器
              if (timeoutId) clearTimeout(timeoutId);

              // 即使出错，也返回已累积的数据
              if (allEvents.length > 0) {
                sendResponse({
                  success: true,
                  data: allEvents,
                });
              } else {
                throw error;
              }
            } finally {
              reader.releaseLock();
            }
          } else {
            // 普通响应处理
            const responseText = await response.text();

            // 尝试解析响应
            let responseData;
            try {
              responseData = JSON.parse(responseText);
            } catch (e) {
              responseData = responseText;
            }

            sendResponse({
              success: true,
              data: responseData,
            });
          }
        } catch (error) {
          sendResponse({
            success: false,
            error: error.message,
          });
        }
      })();
      return true; // 保持消息通道开放
    } else if (message.type === "sendAIRequestStreaming") {
      // 处理发送 AI 流式请求的请求
      (async () => {
        try {
          const { url, requestData, apiKey } = message.data;

          const response = await fetch(url, {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
              Authorization: `Bearer ${apiKey}`,
            },
            body: JSON.stringify(requestData),
          });

          if (!response.ok) {
            const errorText = await response.text();
            throw new Error(`HTTP error! status: ${response.status}, response: ${errorText}`);
          }

          // 检查是否是流式响应
          const contentType = response.headers.get("content-type");

          if (contentType && contentType.includes("text/event-stream")) {
            // 流式响应处理 - 实时发送每个数据块
            const reader = response.body?.getReader();
            if (!reader) {
              throw new Error("无法获取响应流");
            }

            try {
              let isReading = true;

              while (isReading) {
                const { done, value } = await reader.read();

                if (done) {
                  isReading = false;
                  break;
                }

                const chunk = new TextDecoder().decode(value);
                const lines = chunk.split("\n");

                for (const line of lines) {
                  if (line.startsWith("data: ")) {
                    const data = line.slice(6);

                    if (data === "[DONE]") {
                      // 数据接收完成，向所有标签页发送完成信号
                      chrome.tabs.query({}, (tabs) => {
                        tabs.forEach((tab) => {
                          if (tab.id) {
                            chrome.tabs
                              .sendMessage(tab.id, {
                                type: "aiStreamingData",
                                taskId: message.data.taskId,
                                data: "",
                                isComplete: true,
                              })
                              .catch(() => {
                                // 忽略发送失败的错误
                              });
                          }
                        });
                      });

                      sendResponse({
                        success: true,
                        data: "[DONE]",
                        isComplete: true,
                      });
                      return;
                    }

                    try {
                      const parsed = JSON.parse(data);

                      // 实时发送每个数据块到 content script
                      if (parsed.event === "message" && parsed.answer) {
                        // 向所有标签页发送流式数据
                        chrome.tabs.query({}, (tabs) => {
                          tabs.forEach((tab) => {
                            if (tab.id) {
                              chrome.tabs
                                .sendMessage(tab.id, {
                                  type: "aiStreamingData",
                                  taskId: message.data.taskId,
                                  data: parsed.answer,
                                  isStreaming: true,
                                })
                                .catch(() => {
                                  // 忽略发送失败的错误
                                });
                            }
                          });
                        });
                      }
                    } catch (e) {
                      // 忽略解析错误
                    }
                  }
                }
              }

              // 流式读取完成，向所有标签页发送完成信号
              chrome.tabs.query({}, (tabs) => {
                tabs.forEach((tab) => {
                  if (tab.id) {
                    chrome.tabs
                      .sendMessage(tab.id, {
                        type: "aiStreamingData",
                        taskId: message.data.taskId,
                        data: "",
                        isComplete: true,
                      })
                      .catch(() => {
                        // 忽略发送失败的错误
                      });
                  }
                });
              });

              sendResponse({
                success: true,
                data: "",
                isComplete: true,
              });
            } catch (error) {
              sendResponse({
                success: false,
                error: error.message,
              });
            } finally {
              reader.releaseLock();
            }
          } else {
            // 普通响应处理
            const responseText = await response.text();

            // 尝试解析响应
            let responseData;
            try {
              responseData = JSON.parse(responseText);
            } catch (e) {
              responseData = responseText;
            }

            sendResponse({
              success: true,
              data: responseData,
            });
          }
        } catch (error) {
          sendResponse({
            success: false,
            error: error.message,
          });
        }
      })();
      return true; // 保持消息通道开放
    } else if (message.type === "chatKnowInfo") {
      sendMessageToPopup(message);
    }
    // 截图  接受content,js发送的截图数据并转发到popup
    if (message.imgUrl) {
      sendMessageToPopup(message);
    }

    /** 注册图片提取监听器 */
    if (message.extractImg64) {
      chrome.storage.local.set({ extractImg64: message.extractImg64 });
      chrome.storage.local.set({ isImgType: "1" });
      chrome.windows.getCurrent((res) => {
        chrome.sidePanel.open({
          windowId: res.id || -1,
        });
      });
    }

    if (message.action === "getTabs") {
      // 查询所有标签页
      browser.tabs &&
        browser.tabs.query({}).then((tabs) => {
          const data = tabs.filter((item) => !["设置 - AI办公助手", "扩展程序", "新标签页"].includes(item.title));
          sendResponse(data);
        });
    } else if (message.action === "getSelectedTabsContent") {
      const tabIds = message.tabIds;
      const promises = tabIds.map((tabId) => {
        return new Promise((resolve) => {
          chrome.scripting.executeScript(
            {
              target: { tabId: tabId },
              func: () => {
                const linkList = document.querySelectorAll("link");
                const hrefs = Array.from(linkList).map((link) => link.href || "");
                let iconLink = "";
                let i = 0;
                hrefs.forEach((item, index) => {
                  if (item.includes(".")) {
                    let arr = item.split(".");
                    if (arr[arr.length - 1] == "svg" || arr[arr.length - 1] == "ico") {
                      if (i < 1) {
                        i = index + 1;
                      }
                    }
                  }
                });
                if (i == 0) {
                  iconLink = "";
                } else {
                  iconLink = hrefs[i - 1] ? hrefs[i - 1] : "";
                }
                return {
                  title: document.title,
                  innerText: document.body.innerText,
                  icon: iconLink || "",
                };
              },
            },
            ([result]) => {
              resolve({ tabId: tabId, innerText: result.result });
            },
          );
        });
      });
      Promise.all(promises).then((results) => {
        sendResponse(results);
      });
    }

    // 清除Cookie
    if (message.ChromeClearCookie) {
      browser.cookies.remove({
        url: import.meta.env["VITE_AUTHORIZE_DOMAIN"],
        name: "userInfo",
      });
      browser.cookies.remove({
        url: import.meta.env["VITE_AUTHORIZE_DOMAIN"],
        name: import.meta.env["VITE_API_HEADER_KEY"],
      });
      browser.cookies.remove({
        url: import.meta.env["VITE_AUTHORIZE_DOMAIN"],
        name: "tokenObj",
      });
      browser.storage.local.remove("user");
    }
    if (message.type === "wakeUp") {
      sendResponse({ status: "success" }); // 向发送者返回响应
    }
    return true;
  });

  // 标记消息监听器已注册
  swManager.markInitialized("messageListenerRegistered", true);
  console.debug("消息监听器注册完成");
}

export default defineBackground(() => {
  // 使用状态管理器检查是否已经初始化过
  if (swManager.isInitialized("backgroundInitialized")) {
    console.debug("Background script 已经初始化过，跳过重复初始化");
    return;
  }

  console.debug("Background script 开始初始化...");

  // 使用安全初始化方法
  const success = swManager.safeInit("backgroundInitialized", () => {
    createTabs();
    initializingCrxBehavior();

    // 使用状态管理器安全注册各个组件
    swManager.safeInit("fetchHandlerRegistered", registerFetchMessageHandler);
    swManager.safeInit("sseHandlerRegistered", registerFetchSSEMessageHandler);

    initializeAlarm();
    publicMessageListener(); // 这个函数内部已经有状态检查
    registerContextMenu();
  });

  if (success) {
    console.debug("Background script 初始化完成");
  } else {
    console.error("Background script 初始化失败");
  }
});
