import React, { useEffect, useRef, useState } from "react";
import { <PERSON><PERSON>, Button, List, Typography, Flex, Card, Divider, theme, Image, message, Spin, Radio } from "antd";
import { CloseOutlined } from "@ant-design/icons";
import { wordList, editWord, delWord } from "@/api/smartMenu";
import "./index.less";
import EmptyData from "@/components/EmptyData";
const { Text } = Typography;
const { useToken } = theme;
const FileRule = () => {
  const { token } = useToken();
  const [menuData, setMenuData] = useState({
    isFileRule: false,
    isFileCheck: false,
    ruleFun: 1, // 1 正则，2 AI
    ruleList: [],
  });
  const [firstData, setFirstData] = useState<any>({});
  const [listLoading, setListLoading] = useState(false);
  const hasInitialized = useRef(false);

  useEffect(() => {
    if (!hasInitialized.current) {
      hasInitialized.current = true;
      return; // 跳过第一次执行
    }
    browser.storage.local.set({
      fileRule: {
        isFileRule: menuData?.isFileRule,
        isFileCheck: menuData?.isFileCheck,
        ruleFun: menuData?.ruleFun || 1,
        // ruleList: menuData.ruleList.map((item) => item.domainUrl),
        ruleList: menuData?.ruleList.filter((item) => item != null),
      },
    });
  }, [menuData]);

  // 获取列表
  const getWordList = async () => {
    // setListLoading(true);
    // wordList({}).then((res) => {
    //   const [, ...list] = res.data;
    //   setFirstData(res.data[0]);
    //   setListLoading(false);
    //   setMenuData({
    //     ...menuData,
    //     ...{ isFileRule: res.data[0].enabled, ruleList: list },
    //   });
    // });
    await browser.storage.local.get(["fileRule"]).then((result) => {
      console.log("result", result);
      if (result && Object.keys(result).length > 0 && result.constructor === Object) {
        setMenuData((prev) => ({
          ...prev,
          isFileRule: result?.fileRule?.isFileRule || false,
          isFileCheck: result?.fileRule?.isFileCheck || false,
          ruleFun: result?.fileRule?.ruleFun || 1,
          ruleList: result.fileRule.ruleList.filter((item) => item) || [],
        }));
      } else {
        setMenuData(() => ({
          isFileRule: false,
          isFileCheck: false,
          ruleFun: 1,
          ruleList: [],
        }));
      }
    });
  };

  // 删除隐藏的网站
  const delWebsite = (item: any) => {
    // setListLoading(true);
    // delWord([id]).then((res) => {
    //   if (res.code == 200) {
    //     message.open({
    //       type: "success",
    //       content: "删除成功！",
    //     });
    //     setListLoading(false);
    //     getWordList();
    //   }
    // });
    // getWordList();
    setMenuData((prev) => ({
      ...prev,
      ruleList: prev.ruleList.filter((url) => url !== item),
    }));
  };

  // 文件规则开关
  const switchChange = (checked: boolean) => {
    // setListLoading(true);
    setMenuData({ ...menuData, ...{ isFileRule: checked } });
    // editWord({ id: firstData.id, configType: "GLOBAL", enabled: checked }).then((res) => {
    //   if (res.code == 200) {
    //     message.open({
    //       type: "success",
    //       content: "修改成功！",
    //     });
    //     setListLoading(false);
    //   }
    // });
  };
  // 合同校验开关
  const switchChange2 = (checked: boolean) => {
    setMenuData({ ...menuData, ...{ isFileCheck: checked } });
  };
  useEffect(() => {
    getWordList();
  }, []);
  return (
    <Flex vertical gap={token.padding} className="smart-menu">
      {/* 文本描述区域 */}
      <Card>
        <Flex justify="center" style={{ height: "120px" }}>
          <Image
            src={browser.runtime.getURL("/images/setup/rule.jpg")}
            alt="图片"
            style={{ width: "auto", height: "120px" }}
          />
        </Flex>
      </Card>

      <Spin spinning={listLoading}>
        {/* 文件规则开关 */}
        <Card>
          <>
            <Flex vertical gap={token.paddingSM}>
              <Flex align="center" justify="space-between">
                <Text style={{ fontSize: token.fontSizeLG }}>文件规则</Text>
                <Switch value={menuData.isFileRule} onChange={switchChange} />
              </Flex>
              <Text type="secondary">开启时，在浏览器中上传文件时，文件操作规则会自动适配</Text>
            </Flex>
            {menuData.isFileRule && (
              <Flex style={{ marginTop: token.marginLG }} gap={token.marginLG} justify="space-between">
                <Text style={{ fontSize: token.fontSizeLG }}>检测方式</Text>
                <Flex>
                  <Radio.Group
                    name="radiogroup"
                    defaultValue={1}
                    value={menuData?.ruleFun || 1}
                    onChange={(e) => {
                      setMenuData({ ...menuData, ...{ ruleFun: e.target.value } });
                    }}
                    options={[
                      { value: 1, label: "正则检索" },
                      { value: 2, label: "AI检索" },
                    ]}
                  />
                </Flex>
              </Flex>
            )}
          </>
          <Divider />
          {/* 网站禁用列表 */}
          {menuData.isFileRule && (
            <div>
              {menuData.ruleList.length > 0 ? (
                <Flex vertical gap={token.padding}>
                  <Text strong>在网站上禁用</Text>
                  <List
                    dataSource={menuData.ruleList}
                    renderItem={(item, index) => (
                      <List.Item className="list-item" key={index}>
                        {/* <Text>{item.domainUrl}</Text> */}
                        <Text>{item}</Text>
                        <CloseOutlined
                          className="colse"
                          style={{ color: token.colorTextDescription, cursor: "pointer" }}
                          onClick={() => {
                            delWebsite(item);
                          }}
                        />
                      </List.Item>
                    )}
                  />
                </Flex>
              ) : (
                <EmptyData description={"暂无数据"} />
              )}
            </div>
          )}
        </Card>
        {/* 文件校验开关 */}
        <Card>
          <>
            <Flex vertical gap={token.paddingSM}>
              <Flex align="center" justify="space-between">
                <Text style={{ fontSize: token.fontSizeLG }}>合同校验</Text>
                <Switch value={menuData.isFileCheck} onChange={switchChange2} />
              </Flex>
              <Text type="secondary">开启时，在浏览器中上传合同时，合同会自动校验</Text>
            </Flex>
          </>
        </Card>
      </Spin>
    </Flex>
  );
};

export default FileRule;
