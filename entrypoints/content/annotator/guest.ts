import { TinyEmitter } from "tiny-emitter";
import { ListenerCollection } from "../shared/listener-collection";
import { PortFinder, PortRPC } from "../shared/messaging";
import type {
  AnnotationData,
  Annotator,
  Anchor,
  Destroyable,
  DocumentInfo,
  Integration,
  SidebarLayout,
} from "../types/annotator";
import type { Target } from "../types/api";
import type {
  HostToGuestEvent,
  GuestToHostEvent,
  GuestToSidebarEvent,
  SidebarToGuestEvent,
} from "../types/port-rpc-events";
import { Adder } from "./adder";
import { TextRange } from "./anchoring/text-range";
import { BucketBarClient } from "./bucket-bar-client";
import {
  getHighlightsContainingNode,
  highlightRange,
  removeAllHighlights,
  removeHighlights,
  setHighlightsFocused,
  wholeTextNodesInRange,
} from "./highlighter";
import { HTMLIntegration } from "./integrations";
import { itemsForRange, isSelectionBackwards, selectionFocusRect, selectedRange } from "./range-util";
import { SelectionObserver } from "./selection-observer";
import { normalizeURI } from "./util/url";
import { getUrlNoQuery, NOTE_THEME, NOTE_MIN_WIDTH, NOTE_MIN_HEIGHT } from "@/utils/notes";
import type { Note } from "@/types/note";
import { AgentInfo } from "@/types/chat";

/** 高亮器创建的 HTML 元素，带有相关的注释。 */
type AnnotationHighlight = HTMLElement & { _annotation?: AnnotationData };

/** 返回与选定文本相关的所有注释标签。 */
function annotationsForSelection(): string[] {
  const tags = itemsForRange(selectedRange() ?? new Range(), (node) => (node as AnnotationHighlight)._annotation?.$tag);
  return tags;
}

/**
 * 返回包含给定 DOM 节点的任何高亮的注释标签。
 */
function annotationsAt(node: Node): string[] {
  const items = getHighlightsContainingNode(node)
    .map((h) => (h as AnnotationHighlight)._annotation)
    .filter((ann) => ann !== undefined)
    .map((ann) => ann?.$tag);
  return items as string[];
}

/**
 * 将锚点的相关文档区域解析为具体的 `Range`。
 *
 * 如果锚点创建后文档发生了变更，导致锚点无效，则可能会失败。
 */
function resolveAnchor(anchor: Anchor): Range | null {
  if (!anchor.range) {
    return null;
  }
  try {
    return anchor.range.toRange();
  } catch {
    return null;
  }
}

function removeTextSelection() {
  document.getSelection()?.removeAllRanges();
}

/**
 * Hypothesis 客户端配置的子集，用于 {@link Guest}。
 */
export type GuestConfig = {
  /**
   * 在尝试锚定注释之前，Guest 应等待的 Promise。
   */
  contentReady?: Promise<void>;
};

/**
 * 客户端在即将滚动高亮到视图中时分派的事件。
 *
 * 主页面可以监听此事件以显示内容（如果尚未可见）。如果内容将异步显示，可以使用 {@link waitUntil} 通知客户端何时准备就绪。
 *
 * 为了更灵活，主页面可以通过在事件上调用 {@link Event.preventDefault} 完全接管滚动到范围的操作。
 */
export class ScrollToRangeEvent extends CustomEvent<Range> {
  private _ready: Promise<void> | null;

  /**
   * @param range - Hypothesis 将滚动到视图中的 DOM 范围。
   */
  constructor(range: Range) {
    super("scrolltorange", {
      bubbles: true,
      cancelable: true,
      detail: range,
    });

    this._ready = null;
  }

  /**
   * 如果使用 {@link waitUntil} 延迟了滚动，则返回在高亮滚动到视图之前必须解析的 Promise。
   */
  get ready(): Promise<void> | null {
    return this._ready;
  }

  /**
   * 提供一个 Promise，当与事件范围相关的内容准备好滚动到视图时解析。
   */
  waitUntil(ready: Promise<void>) {
    this._ready = ready;
  }
}

/**
 * `Guest` 是注释器的核心类，负责在从侧边栏获取注释时在文档中定位注释、为其渲染高亮并处理与高亮的后续交互。
 *
 * 它还负责监听当前选择的更改并触发显示创建新注释的控件。当点击这些控件之一时，它会创建新注释并将其发送到侧边栏。
 *
 * 在浏览器标签页中，通常每个加载 Hypothesis 的框架有一个 `Guest` 实例（并非所有框架都启用注释）。在一个框架中，通常是顶级框架，还会有一个显示侧边栏应用和周围 UI 的 `Sidebar` 类实例。每个框架中的 `Guest` 实例在初始化时连接到侧边栏和主框架。
 */
export class Guest extends TinyEmitter implements Annotator, Destroyable {
  public element: HTMLElement;

  /** 当前文本选择的范围。 */
  public selectedRanges: Range[];

  /**
   * 通过将注释选择器解析为文档中的位置生成的锚点。这些由 `anchor` 添加，由 `detach` 移除。
   *
   * 每个注释 `Target` 通常有一个锚点，这通常意味着每个注释有一个锚点。
   */
  public anchors: Anchor[];

  /**
   * 在尝试锚定注释之前，Guest 将等待的 Promise。
   */
  private _contentReady?: Promise<void>;

  private _adder: Adder;
  private _hostFrame: Window;
  private _isAdderVisible: boolean;
  private _range: Range;
  private _selectedText: string;
  private _informHostOnNextSelectionClear: boolean;
  private _selectionObserver: SelectionObserver;

  /**
   * 当前在 Guest 中锚定或正在锚定的注释标签。
   */
  private _annotations: Set<string>;
  private _frameIdentifier: string | null;
  private _portFinder: PortFinder;

  /**
   * 处理 Guest 中文档类型特定功能的集成。
   */
  private _integration: Integration;

  /** 主客通信通道。 */
  private _hostRPC: PortRPC<HostToGuestEvent, GuestToHostEvent>;

  /** 客侧边栏通信通道。 */
  private _sidebarRPC: PortRPC<SidebarToGuestEvent, GuestToSidebarEvent>;

  /**
   * 从主框架接收到的最新侧边栏布局信息。
   */
  private _sidebarLayout: SidebarLayout | null;

  private _bucketBarClient: BucketBarClient;

  private _listeners: ListenerCollection;

  /**
   * 当前悬停注释的标签。这用于在关联的注释在侧边栏中已经悬停时正确设置新高亮的悬停状态。
   */
  private _hoveredAnnotations: Set<string>;
  private _createAnnotationBase: {
    type: string;
    agent: null | AgentInfo;
    note: null | Note;
    target: null | Target[];
    info: null | DocumentInfo;
  };

  /**
   * @param element -
   *   `Guest` 实例应能够锚定或创建注释的根元素。在普通网页中，这通常是 `document.body`。
   * @param [config]
   * @param [hostFrame] -
   *   与此 Guest 关联的主框架。预计这是 Guest 框架的祖先。它可以是同源或跨源的。
   */
  constructor(element: HTMLElement, config: GuestConfig = {}, hostFrame: Window = window) {
    super();
    this.element = element;
    this._contentReady = config.contentReady;
    this._hostFrame = hostFrame;
    this._isAdderVisible = false;
    this._informHostOnNextSelectionClear = true;
    this.selectedRanges = [];
    this._createAnnotationBase = {
      type: "",
      agent: null,
      note: null,
      target: null,
      info: null,
    };
    this._adder = new Adder(this.element, {
      onAnnotate: (type, obj?: AgentInfo) => this.createAnnotation(type, obj, false),
      onHighlight: (type) => this.createAnnotation(type, null, false),

      // 当触发“显示”按钮时，打开侧边栏并选择注释。还将键盘焦点转移到第一个选定的注释。这对于屏幕阅读器用户来说是一个重要的便利，因为它为他们提供了一种从文档中的高亮快速导航到侧边栏中相应评论的高效方式。
      onShowAnnotations: (tags) => this.selectAnnotations(tags),
    });

    this._selectionObserver = new SelectionObserver((range) => {
      if (range) {
        this._onSelection(range);
      } else {
        this._onClearSelection();
      }
    });

    this.anchors = [];
    this._annotations = new Set();

    // 如果可用，设置框架标识符。
    // “顶级” Guest 实例将其设置为 null，因为它在顶级框架中，而不是子框架中
    this._frameIdentifier = null;

    this._portFinder = new PortFinder({
      hostFrame: this._hostFrame,
      source: "guest",
      sourceId: this._frameIdentifier ?? undefined,
    });
    this._integration = new HTMLIntegration({});
    this._integration.on("uriChanged", () => this._sendDocumentInfo());

    this._hostRPC = new PortRPC();
    this._connectHost(hostFrame);

    this._sidebarRPC = new PortRPC();
    this._sidebarLayout = null;
    this._connectSidebar();

    this._bucketBarClient = new BucketBarClient({
      contentContainer: this._integration.contentContainer(),
      hostRPC: this._hostRPC,
    });

    // 在根元素上设置事件处理程序
    this._listeners = new ListenerCollection();
    this._setupElementEvents();

    this._hoveredAnnotations = new Set();

    // 初始化iframe监听
    this._setupIframeListeners();
  }

  // 为文档和高亮上的点击、点击等添加 DOM 事件监听器。
  _setupElementEvents() {
    this._listeners.add(window, "resize", () => this._repositionAdder());
  }

  // 设置iframe监听器
  _setupIframeListeners() {
    // 监听来自iframe的消息
    this._listeners.add(window, "message", (event) => {
      if (event.data?.type === "iframe-text-selected") {
        console.debug("主页面收到iframe选择消息:", event.data);
        this._handleIframeSelection(event.data);
      } else if (event.data?.type === "iframe-selection-cleared") {
        console.debug("主页面收到iframe清除选择消息");
        this._onClearSelection();
      } else if (event.data?.type === "iframe-listener-ready") {
        console.debug("iframe监听器就绪:", event.data.iframeId);
      }
    });

    // 监听iframe的加载和DOM变化
    const observeIframes = () => {
      // 扩大搜索范围，包括Shadow DOM
      const iframes = this._getAllIframes();
      iframes.forEach((iframe) => {
        this._setupIframeSelectionListener(iframe);
      });
    };

    // 初始检查
    observeIframes();

    // 创建多个observer来监听不同的DOM变化
    const observers: MutationObserver[] = [];

    // 主文档observer
    const mainObserver = new MutationObserver((mutations) => {
      this._handleMutations(mutations);
    });

    mainObserver.observe(document.documentElement, {
      childList: true,
      subtree: true,
    });
    observers.push(mainObserver);

    // 定期检查新的iframe（处理异步加载的情况）
    const intervalId = setInterval(() => {
      console.debug("定期检查iframe...");
      const currentIframes = this._getAllIframes();
      console.debug(`发现 ${currentIframes.length} 个iframe`);

      currentIframes.forEach((iframe, index) => {
        console.debug(
          `检查iframe ${index + 1}:`,
          iframe.src || iframe.getAttribute("srcdoc")?.substring(0, 30) + "...",
        );

        // 检查是否已经处理过这个iframe
        if (!iframe.hasAttribute("data-sino-iframe-processed")) {
          console.debug(`处理新iframe ${index + 1}`);
          this._setupIframeSelectionListener(iframe);
        } else {
          console.debug(`iframe ${index + 1} 已处理过`);
        }
      });
    }, 2000); // 增加检查间隔到2秒

    // 在销毁时清理所有资源
    this._listeners.add(window, "beforeunload", () => {
      observers.forEach((observer) => observer.disconnect());
      clearInterval(intervalId);
    });
  }

  // 获取页面中所有的iframe，包括Shadow DOM中的
  _getAllIframes(): HTMLIFrameElement[] {
    const iframes: HTMLIFrameElement[] = [];

    // 获取主文档中的iframe
    const mainIframes = document.querySelectorAll("iframe");
    mainIframes.forEach((iframe) => iframes.push(iframe as HTMLIFrameElement));

    // 递归搜索Shadow DOM中的iframe
    const searchShadowDOM = (root: Document | ShadowRoot) => {
      const elements = root.querySelectorAll("*");
      elements.forEach((element) => {
        // 检查当前元素是否是iframe
        if (element.tagName === "IFRAME") {
          iframes.push(element as HTMLIFrameElement);
        }

        // 如果元素有Shadow DOM，递归搜索
        if (element.shadowRoot) {
          searchShadowDOM(element.shadowRoot);
          // 在Shadow DOM中查找iframe
          const shadowIframes = element.shadowRoot.querySelectorAll("iframe");
          shadowIframes.forEach((iframe) => iframes.push(iframe as HTMLIFrameElement));
        }
      });
    };

    // 搜索Shadow DOM
    searchShadowDOM(document);

    return iframes;
  }

  // 处理DOM变化
  _handleMutations(mutations: MutationRecord[]) {
    mutations.forEach((mutation) => {
      mutation.addedNodes.forEach((node) => {
        if (node.nodeType === Node.ELEMENT_NODE) {
          const element = node as Element;

          // 直接是iframe
          if (element.tagName === "IFRAME") {
            const iframe = element as HTMLIFrameElement;
            if (!iframe.hasAttribute("data-sino-iframe-processed")) {
              iframe.setAttribute("data-sino-iframe-processed", "true");
              this._setupIframeSelectionListener(iframe);
            }
          } else {
            // 检查新添加的元素是否包含iframe
            const iframes = element.querySelectorAll("iframe");
            iframes.forEach((iframe) => {
              if (!iframe.hasAttribute("data-sino-iframe-processed")) {
                iframe.setAttribute("data-sino-iframe-processed", "true");
                this._setupIframeSelectionListener(iframe as HTMLIFrameElement);
              }
            });

            // 如果元素有Shadow DOM，也要检查
            if (element.shadowRoot) {
              const shadowIframes = element.shadowRoot.querySelectorAll("iframe");
              shadowIframes.forEach((iframe) => {
                if (!iframe.hasAttribute("data-sino-iframe-processed")) {
                  iframe.setAttribute("data-sino-iframe-processed", "true");
                  this._setupIframeSelectionListener(iframe as HTMLIFrameElement);
                }
              });
            }
          }
        }
      });
    });
  }

  // 为iframe设置选择监听器
  _setupIframeSelectionListener(iframe: HTMLIFrameElement) {
    console.debug("尝试设置iframe选择监听器:", iframe.src || iframe.getAttribute("srcdoc")?.substring(0, 50) + "...");

    // 检查是否已经处理过
    if (iframe.hasAttribute("data-sino-iframe-processed")) {
      console.debug("iframe已经处理过，跳过");
      return;
    }

    // 标记为已处理
    iframe.setAttribute("data-sino-iframe-processed", "true");

    try {
      // 检查是否可以访问iframe内容（同源检查）
      const iframeDoc = iframe.contentDocument || iframe.contentWindow?.document;
      if (!iframeDoc) {
        console.debug("iframe文档未就绪，等待加载完成");

        // 如果无法立即访问，等待iframe加载完成
        const loadHandler = () => {
          console.debug("iframe加载完成，重新尝试设置监听器");
          // 移除已处理标记，允许重新处理
          iframe.removeAttribute("data-sino-iframe-processed");
          this._setupIframeSelectionListener(iframe);
        };

        iframe.addEventListener("load", loadHandler, { once: true });

        // 设置超时重试机制
        setTimeout(() => {
          if (!iframe.hasAttribute("data-sino-script-injected")) {
            console.debug("iframe加载超时，强制重试");
            iframe.removeAttribute("data-sino-iframe-processed");
            this._setupIframeSelectionListener(iframe);
          }
        }, 3000); // 增加超时时间到3秒

        return;
      }

      // 检查文档是否完全加载
      if (iframeDoc.readyState !== "complete") {
        console.debug("iframe文档未完全加载，等待完成");
        const readyHandler = () => {
          console.debug("iframe文档加载完成，注入脚本");
          this._injectSelectionScript(iframe, iframeDoc);
        };

        if (iframeDoc.readyState === "loading") {
          iframeDoc.addEventListener("DOMContentLoaded", readyHandler, { once: true });
        } else {
          // 文档已经在interactive状态，直接注入
          setTimeout(() => readyHandler(), 100);
        }
        return;
      }

      // 注入选择监听脚本到iframe
      this._injectSelectionScript(iframe, iframeDoc);
    } catch (error) {
      // 跨域iframe无法访问，忽略
      console.debug("无法访问iframe内容（可能是跨域）:", error);
    }
  }

  // 向iframe注入选择监听脚本
  _injectSelectionScript(iframe: HTMLIFrameElement, iframeDoc: Document) {
    // 检查是否已经注入过脚本
    if (iframeDoc.querySelector("[data-sino-selection-listener]")) {
      console.debug("iframe脚本已存在，跳过注入");
      return;
    }

    console.debug("开始向iframe注入选择监听脚本");

    // 创建脚本元素
    const script = iframeDoc.createElement("script");
    script.setAttribute("data-sino-selection-listener", "true");
    script.textContent = `
      (function() {
        console.debug("iframe选择监听脚本已加载");
        let selectionTimeout;

        function handleSelectionChange() {
          clearTimeout(selectionTimeout);
          selectionTimeout = setTimeout(() => {
            const selection = document.getSelection();
            if (selection && selection.toString().trim()) {
              console.debug("iframe中检测到文本选择:", selection.toString().substring(0, 50));

              try {
                const range = selection.getRangeAt(0);
                const rect = range.getBoundingClientRect();
                const iframeRect = window.frameElement ? window.frameElement.getBoundingClientRect() : { top: 0, left: 0 };

                // 计算相对于主页面的位置
                let iframeRect = { top: 0, left: 0 };
                if (window.frameElement) {
                  iframeRect = window.frameElement.getBoundingClientRect();
                  console.debug("iframe位置:", iframeRect);
                } else {
                  console.debug("无法获取iframe元素");
                }

                const scrollX = window.parent.scrollX || window.parent.pageXOffset || 0;
                const scrollY = window.parent.scrollY || window.parent.pageYOffset || 0;

                const absoluteRect = {
                  top: rect.top + iframeRect.top + scrollY,
                  left: rect.left + iframeRect.left + scrollX,
                  width: rect.width,
                  height: rect.height,
                  bottom: rect.bottom + iframeRect.top + scrollY,
                  right: rect.right + iframeRect.left + scrollX
                };

                console.debug("选择区域位置:", rect);
                console.debug("计算后的绝对位置:", absoluteRect);

                const message = {
                  type: "iframe-text-selected",
                  selectedText: selection.toString(),
                  rect: absoluteRect,
                  isBackwards: selection.anchorOffset > selection.focusOffset,
                  iframeId: (window.frameElement && window.frameElement.id) || 'iframe-' + Date.now(),
                  timestamp: Date.now()
                };

                console.debug("向主页面发送选择消息:", message);
                window.parent.postMessage(message, "*");
              } catch (error) {
                console.error("处理iframe选择时出错:", error);
              }
            } else {
              console.debug("iframe中选择被清除");
              window.parent.postMessage({
                type: "iframe-selection-cleared",
                timestamp: Date.now()
              }, "*");
            }
          }, 100);
        }

        document.addEventListener("selectionchange", handleSelectionChange);
        console.debug("iframe选择监听器已注册");

        // 清理函数
        window.addEventListener("beforeunload", () => {
          document.removeEventListener("selectionchange", handleSelectionChange);
          console.debug("iframe选择监听器已清理");
        });

        // 发送就绪消息
        window.parent.postMessage({
          type: "iframe-listener-ready",
          iframeId: (window.frameElement && window.frameElement.id) || 'iframe-' + Date.now(),
          timestamp: Date.now()
        }, "*");
      })();
    `;

    try {
      // 将脚本添加到iframe的head中
      (iframeDoc.head || iframeDoc.documentElement).appendChild(script);

      // 标记iframe已处理
      iframe.setAttribute("data-sino-script-injected", "true");
      console.debug("iframe脚本注入成功");
    } catch (error) {
      console.error("iframe脚本注入失败:", error);
    }
  }

  // 处理来自iframe的选择事件
  _handleIframeSelection(data: any) {
    const { selectedText, rect, isBackwards, iframeId } = data;

    console.debug("处理iframe选择事件:", { selectedText: selectedText.substring(0, 30), rect, isBackwards });

    // 创建一个模拟的DOMRect对象
    const mockRect: DOMRect = {
      top: rect.top,
      left: rect.left,
      width: rect.width,
      height: rect.height,
      bottom: rect.bottom,
      right: rect.right,
      x: rect.left,
      y: rect.top,
      toJSON: () => rect,
    };

    // 创建一个模拟的Range对象用于createAnnotation
    const mockRange = {
      getBoundingClientRect: () => mockRect,
      toString: () => selectedText,
    } as Range;

    // 设置选中的文本和范围信息
    this._selectedText = selectedText;
    this._range = mockRange; // 设置模拟的range对象
    this.selectedRanges = []; // iframe中的选择无法直接转换为Range对象

    // 通知主机框架有文本被选中
    this._hostRPC.call("textSelected");

    // 设置相关注释
    this._adder.annotationsForSelection = []; // iframe中暂时无法获取现有注释

    // 显示添加器
    this._isAdderVisible = true;

    console.debug("准备显示AdderToolbar，位置:", mockRect);

    // 简化智能菜单逻辑，确保iframe中的选择能够显示工具栏
    const arr = sessionStorage.getItem("temporaryList") ? JSON.parse(sessionStorage.getItem("temporaryList")) : [];
    browser.storage.local
      .get(["smartMenu"])
      .then((result) => {
        console.debug("智能菜单设置:", result?.smartMenu);

        // 对于iframe中的选择，优先显示工具栏
        let shouldShow = true;

        if (result?.smartMenu) {
          let url = window.location.href.split("?")[0];
          // 检查是否在禁用列表中
          if (result.smartMenu.websiteList && result.smartMenu.websiteList.includes(url)) {
            shouldShow = false;
            console.debug("当前网站在禁用列表中");
          }
          if (arr.includes(url)) {
            shouldShow = false;
            console.debug("当前网站在临时禁用列表中");
          }
        }

        if (shouldShow) {
          console.debug("显示AdderToolbar");
          this._adder.show(mockRect, isBackwards);
        } else {
          console.debug("智能菜单设置阻止显示AdderToolbar");
        }
      })
      .catch((error) => {
        console.error("获取智能菜单设置失败:", error);
        // 如果获取设置失败，默认显示工具栏
        console.debug("默认显示AdderToolbar");
        this._adder.show(mockRect, isBackwards);
      });

    // 存储选中的文本到本地存储
    browser.storage.local.set({ selectedText: selectedText });
  }

  /**
   * 检索当前文档的元数据。
   */
  async getDocumentInfo(): Promise<DocumentInfo> {
    const [uri, metadata, segmentInfo] = await Promise.all([
      this._integration.uri(),
      this._integration.getMetadata(),
      this._integration.segmentInfo?.(),
    ]);

    return {
      uri: normalizeURI(uri),
      metadata,
      segmentInfo,
      persistent: this._integration.persistFrame?.() ?? false,
    };
  }

  /** 将当前文档 URI 和元数据发送到侧边栏。 */
  async _sendDocumentInfo() {
    const metadata = await this.getDocumentInfo();
    this._sidebarRPC.call("documentInfoChanged", metadata);
  }

  /**
   * 在窗口“resize”事件时调整添加器的位置
   */
  _repositionAdder() {
    if (!this._isAdderVisible) {
      return;
    }
    const range = selectedRange();
    if (range) {
      this._onSelection(range);
    }
  }

  async _connectHost(hostFrame: Window) {
    this._hostRPC.on("clearSelection", () => {
      if (selectedRange()) {
        this._informHostOnNextSelectionClear = false;
        removeTextSelection();
      }
    });

    this._hostRPC.on("setNoteNotice", async (note: Note, target, info) => {
      const annotation: AnnotationData = {
        uri: note.url,
        document: info?.metadata || "",
        target,
        $highlight: false,
        $cluster: "user-annotations",
        $tag: note.id,
        $id: note.id,
        item: note,
      };
      await this.anchor(annotation);
      this._adder.setNotelist(note);
      // 删除文本选择会触发 `SelectionObserver` 回调，这会导致添加器在一段时间后被移除。
      removeTextSelection();
    });

    this._hostRPC.on("delNoteNotice", async (note: Note) => {
      this.detach(note.id);
      this._adder.delNotelistByTag(note.id);
    });

    this._hostRPC.on("hoverAnnotations", (tags: string[]) => this._hoverAnnotations(tags));

    this._hostRPC.on("scrollToAnnotation", (tag: string) => {
      this._scrollToAnnotation(tag);
    });

    this._hostRPC.on("selectAnnotations", (tags: string[]) => this.selectAnnotations(tags));

    this._hostRPC.on("close", () => this.emit("hostDisconnected"));

    // 发现并连接到主框架。所有 RPC 事件必须在创建通道之前注册。
    const hostPort = await this._portFinder.discover("host");
    this._hostRPC.connect(hostPort);
  }

  /**
   * 滚动锚点到视图并通知主页面。
   *
   * 返回一个 Promise，当滚动完成时解析。参见 {@link Integration.scrollToAnchor}。
   */
  private async _scrollToAnchor(anchor: Anchor) {
    const range = resolveAnchor(anchor);
    if (!range) {
      return;
    }

    // 触发主页面可以响应的自定义事件。如果内容在页面的隐藏部分，需要在滚动到视图之前显示出来，这很有用。
    const event = new ScrollToRangeEvent(range);

    const defaultNotPrevented = this.element.dispatchEvent(event);

    if (defaultNotPrevented) {
      await event.ready;
      await this._integration.scrollToAnchor(anchor);
    }
  }

  private async _scrollToAnnotation(tag: string) {
    const anchor = this.anchors.find((a) => a.annotation.$tag === tag);
    if (!anchor?.highlights) {
      return;
    }
    await this._scrollToAnchor(anchor);
  }

  async _connectSidebar() {
    // 处理用户在侧边栏中悬停或点击注释卡片时发送的事件。
    this._sidebarRPC.on("hoverAnnotations", (tags: string[]) => this._hoverAnnotations(tags));

    this._sidebarRPC.on("scrollToAnnotation", (tag: string) => {
      this._scrollToAnnotation(tag);
    });

    this._sidebarRPC.on("deleteAnnotation", (tag: string) => this.detach(tag));

    this._sidebarRPC.on("loadAnnotations", async (annotations: AnnotationData[]) => {
      try {
        await Promise.all(annotations.map((ann) => this.anchor(ann)));
      } catch (e) {
        /* istanbul ignore next */
        console.warn("Failed to anchor annotations:", e);
      }
    });

    // 连接到侧边栏并将文档信息/URI 发送到侧边栏。
    //
    // RPC 调用在建立连接之前是延迟的，因此这些步骤可以按任意顺序完成。
    this._portFinder.discover("sidebar").then((port) => {
      this._sidebarRPC.connect(port);
    });

    this._sendDocumentInfo();
  }

  destroy() {
    this._portFinder.destroy();
    this._hostRPC.destroy();
    this._sidebarRPC.destroy();

    this._listeners.removeAll();

    this._selectionObserver.disconnect();
    this._adder.destroy();
    this._bucketBarClient.destroy();

    removeAllHighlights(this.element);

    this._integration.destroy();
  }
  /**
   * 锚定文档中的注释选择器。
   *
   * 锚定将一组选择器解析为文档中的具体区域，然后对其进行高亮显示。
   *
   * 在重新锚定注释之前，将删除与 `annotation` 关联的任何现有锚点。
   */
  async anchor(annotation: AnnotationData): Promise<Anchor[]> {
    if (this._contentReady) {
      await this._contentReady;
      this._contentReady = undefined;
    }

    /**
     * 将注释的选择器解析为具体的范围。
     */
    const locate = async (target: Target): Promise<Anchor> => {
      // 目前只有带有相关引用的注释可以被锚定。
      // 这是因为引用用于验证其他选择器类型的锚定。
      if (!target.selector || !target.selector.some((s) => s.type === "TextQuoteSelector")) {
        return { annotation, target };
      }

      let anchor: Anchor;
      try {
        const range = await this._integration.anchor(this.element, target.selector);
        // 将 `Range` 转换为 `TextRange`，可以稍后再转换回 `Range`。
        // `TextRange` 表示允许在锚定其他注释期间插入高亮而不会“破坏”此锚点。
        const textRange = TextRange.fromRange(range);
        anchor = { annotation, target, range: textRange };
      } catch (err) {
        anchor = { annotation, target };
      }
      return anchor;
    };

    /**
     * 高亮 `anchor` 所指的文本范围。
     */
    const highlight = (anchor: Anchor) => {
      const range = resolveAnchor(anchor);
      if (!range) {
        return;
      }

      const highlights = highlightRange(
        range,
        anchor.annotation?.$cluster /* cssClass */,
        anchor.annotation?.$id,
      ) as AnnotationHighlight[];
      highlights.forEach((h) => {
        h._annotation = anchor.annotation;
      });
      anchor.highlights = highlights;

      if (this._hoveredAnnotations.has(anchor.annotation.$tag)) {
        setHighlightsFocused(highlights, true);
      }
    };

    // 删除此注释的现有锚点。
    this.detach(annotation.$tag, false /* notify */);

    this._annotations.add(annotation.$tag);

    // 将选择器解析为范围并插入高亮。
    if (!annotation.target) {
      annotation.target = [];
    }
    const anchors = await Promise.all(annotation.target.map(locate));
    // 如果在锚定过程中删除了注释，则不要保存锚点。
    if (!this._annotations.has(annotation.$tag)) {
      return [];
    }

    for (const anchor of anchors) {
      highlight(anchor);
    }

    // 设置指示锚定是否成功的标志。对于每个目标，如果没有选择器（即这是一个页面注释）或我们成功地将选择器解析为范围，则锚定成功。
    annotation.$orphan = anchors.length > 0 && anchors.every((anchor) => anchor.target.selector && !anchor.range);
    this.anchors = this.anchors.concat(anchors);
    this._updateAnchors(this.anchors, true /* notify */);

    return anchors;
  }

  _initNotelist(noteList) {
    this._adder.initNotelist(noteList);
  }
  // 展示隐藏note
  _setNoteShowClose(tag: string, type: boolean) {
    if (["1", "2"].includes(tag)) {
      this._adder.setNoteShowClose(type); // 全部展开收起
    } else {
      this._adder.setNotelistByTag(tag, type); // 单个展开收起
    }
    this._hostRPC.call("setNoteShowClose", tag, type);
  }
  // 删除所有得note
  _delAllNoteNotice(tags: string[]) {
    tags.forEach((tag) => {
      this.detach(tag);
    });
    this._adder.delAllNotelist();
  }

  /**
   * 从文档中删除注释的锚点和相关的高亮。
   *
   * @param [notify] - 仅供内部使用。是否通知主框架锚点的移除。
   */
  detach(tag: string, notify = true) {
    this._annotations.delete(tag);

    const anchors = [] as Anchor[];
    for (const anchor of this.anchors) {
      if (anchor.annotation.$tag !== tag) {
        anchors.push(anchor);
      } else if (anchor.highlights) {
        removeHighlights(anchor.highlights);
      }
    }
    this.anchors = anchors;
    this._updateAnchors(anchors, notify);
  }

  _updateAnchors(anchors: Anchor[], notify: boolean) {
    const bouketAnchors = anchors.filter((x) => {
      if (!x.range) {
        return false;
      }
      const textNodes = wholeTextNodesInRange(x.range.toRange());
      const nodeValue = textNodes[0]?.nodeValue;
      return /^空白元素-\d{13}$/.test(nodeValue);
    });
    if (notify) {
      this._bucketBarClient.update(bouketAnchors);
    }
  }

  /**
   * 创建一个与当前文档选定区域相关的新注释。
   * 主要分为两种情况
   * 1. 创建普通便签，通过手动js创建空白dom,并使用js选中内容，触发浏览器选中内容变化的监听SelectionObserver，触发得到一个根标签target
   * 2. 划词主动触发文本选中触发监听SelectionObserver,展示划词悬浮窗（this._adder.show(focusRect, isBackwards);），点击按钮触发
   *    1）获取划词内容，通过手动js创建空白dom,并使用js选中内容，触发浏览器选中内容变化的监听,得到两个根标签,即target自己创建的依赖dom, relTarget用户选中的文本
   *
   * @param options
   *   @param [options.highlight] - 如果为 true，则新注释将设置 `$highlight` 标志，导致其立即保存而无需提示评论。
   *  @param addType 是否要触发addNoteNotice
   * @return 新注释
   */
  async createAnnotation(type = "PLAIN", agent: AgentInfo | null, addType: boolean, id?) {
    const ranges = this.selectedRanges;
    this.selectedRanges = [];
    const info = await this.getDocumentInfo();
    const root = this.element;
    let baseId = id || new Date().getTime();
    if (!this._createAnnotationBase.type) {
      const rangeSelectors = await Promise.all(ranges.map((range) => this._integration.describe(root, range)));
      const target = rangeSelectors.map((selectors) => ({
        source: info.uri,
        selector: selectors,
      }));
      const rect = this._range.getBoundingClientRect();
      const coordinates = {
        top: rect.top + window.scrollY,
        left: rect.left,
        width: rect.width,
        height: rect.height,
      };
      let quoteContent = this._selectedText.includes("空白元素") ? "" : this._selectedText;
      let note: Note = {
        content: "",
        title: type === "QUOTE" ? quoteContent.substring(0, 15) : type === "AI" ? agent?.title : "",
        quoteContent,
        type,
        displayFlag: 1,
        delEnable: 1,
        noteStyle: {
          noteType: "absolute",
          noteTop: 0,
          noteLeft: 0,
          noteWidth: NOTE_MIN_WIDTH,
          noteHeight: NOTE_MIN_HEIGHT,
        },
        editable: true,
        url: getUrlNoQuery(),
        color: NOTE_THEME,
        tag: JSON.stringify({ target, coordinates, baseId }),
        belongUserId: "",
        readFlag: "",
        count: "",
        promptId: agent?.id || "",
        promptTitle: agent?.title || "",
        promptContent: agent?.tmplContent || agent?.content || "",
      };
      this._createAnnotationBase = {
        type: type,
        agent: agent,
        note: note,
        target: target,
        info: info,
      };
      if (!addType) {
        const newElement = document.createElement(`div-${baseId}`); // 创建空白标签
        newElement.innerHTML = `空白元素-${baseId}`; // 添加内容并隐藏，使其可被选中并且不显示
        newElement.style.position = "absolute"; // 添加定位信息
        newElement.style.opacity = "0"; // 添加定位信息
        newElement.style.pointerEvents = "none"; // 不允许影响再次划词
        newElement.style.left = `${coordinates.left}px`; // 添加坐标信息
        newElement.style.top = `${coordinates.top}px`; // 添加坐标信息
        document.body.append(newElement); // 将新元素插入
        const range = document.createRange(); //
        range.selectNodeContents(newElement);
        const selection = window.getSelection();
        selection.removeAllRanges(); // 清除当前选择
        selection.addRange(range); // 添加新范围
      }
    } else {
      if (addType) {
        const rangeSelectors = await Promise.all(ranges.map((range) => this._integration.describe(root, range)));
        const relTarget: Target[] = rangeSelectors.map((selectors) => ({
          source: info.uri,
          selector: selectors,
        }));
        let arr = relTarget.concat(this._createAnnotationBase.target);
        this._createAnnotationBase.target = arr;
        const tagObject = JSON.parse(this._createAnnotationBase.note.tag);
        this._createAnnotationBase.note.tag = JSON.stringify({
          target: arr,
          coordinates: tagObject.coordinates,
          baseId,
        });
      }
    }
    if (addType && this._createAnnotationBase.type) {
      if (this._createAnnotationBase.type != "QUIZ" && this._createAnnotationBase.type != "AI") {
        window.postMessage(
          {
            type: "addNoteNotice",
            note: this._createAnnotationBase.note,
            target: this._createAnnotationBase.target,
            info: this._createAnnotationBase.info,
            agent: this._createAnnotationBase.agent,
          },
          "*",
        );
      } else {
        window.postMessage(
          {
            type: "setNoteNotice",
            note: {
              ...this._createAnnotationBase.note,
              ...{ displayFlag: 1, id: Date.now(), type: this._createAnnotationBase.type },
            },
            target: this._createAnnotationBase.target,
            info: this._createAnnotationBase.info,
          },
          "*",
        );
      }
      this._createAnnotationBase = {
        type: "",
        agent: null,
        note: null,
        target: null,
        info: null,
      };
    }
  }

  /**
   * 在侧边栏中指示某些注释被聚焦（即关联的文档区域被悬停）。
   */
  _hoverAnnotations(tags: string[]) {
    this._hoveredAnnotations.clear();
    tags.forEach((tag) => this._hoveredAnnotations.add(tag));
    for (const anchor of this.anchors) {
      if (anchor.highlights) {
        const toggle = tags.includes(anchor.annotation.$tag);
        setHighlightsFocused(anchor.highlights, toggle);
      }
    }
    // 便签hover效果
    this._adder.setNoteHover(tags);
  }

  /**
   * 当选择更改时显示或隐藏添加工具栏。
   */
  _onSelection(range: Range) {
    const annotatableRange = this._integration.getAnnotatableRange(range);
    if (!annotatableRange) {
      this._onClearSelection();
      return;
    }

    const selection = document.getSelection()!;
    const isBackwards = isSelectionBackwards(selection);
    const focusRect = selectionFocusRect(selection);

    if (!focusRect) {
      // 选定范围不包含任何文本
      this._onClearSelection();
      return;
    }
    this.selectedRanges = [annotatableRange];
    this._hostRPC.call("textSelected");

    this._adder.annotationsForSelection = annotationsForSelection();
    this._isAdderVisible = true;

    this._range = range;
    this._selectedText = range.toString();
    browser.storage.local.set({ selectedText: this._selectedText });
    if (this._selectedText.length <= 18 && this._selectedText.includes("空白元素")) {
      const baseId = this._selectedText.replace("空白元素-", "");
      this.createAnnotation("PLAIN", null, true, baseId);
    } else {
      // console.log("划词选中")
      // 临时隐藏的网址
      const arr = sessionStorage.getItem("temporaryList") ? JSON.parse(sessionStorage.getItem("temporaryList")) : [];
      // 用户设置的需要隐藏的地址
      browser.storage.local.get(["smartMenu"]).then((result) => {
        if (result?.smartMenu) {
          let url = window.location.href.split("?")[0];
          if (
            (result.smartMenu?.isShowSmartMenu && !result.smartMenu.websiteList.includes(url) && !arr.includes(url)) ||
            Object.keys(result).length === 0
          ) {
            this._adder.show(focusRect, isBackwards);
          }
        } else {
          browser.storage.local.set({
            smartMenu: {
              isShowSmartMenu: true,
              userSwitch: {},
              websiteList: [],
            },
          });
          this._adder.show(focusRect, isBackwards);
        }
      });
    }
  }
  _onClearSelection() {
    this._isAdderVisible = false;
    this._adder.hide();
    this.selectedRanges = [];
    if (this._informHostOnNextSelectionClear) {
      this._hostRPC.call("textUnselected");
    }
    this._informHostOnNextSelectionClear = true;
  }

  /**
   * 在侧边栏中显示给定的注释。
   *
   * 这将在侧边栏中设置一个过滤器，以仅显示选定的注释，并打开侧边栏。可选地，它还可以将键盘焦点转移到第一个选定注释的注释卡上。
   *
   * @param tags
   * @param options
   *   @param [options.toggle] - 切换注释是否被选中，而不仅仅是选择它们
   */
  selectAnnotations(tags: string[]) {
    tags.forEach((id) => {
      this._adder.setNotelistByTag(id);
      this._hostRPC.call("setNoteShowClose", id, true);
    });
  }

  /**
   * 返回当前以悬停状态显示的注释标签。
   */
  get hoveredAnnotationTags(): Set<string> {
    return this._hoveredAnnotations;
  }
}
