import React, { useEffect, useRef, useState, useCallback } from "react";
import SidePanelLayout from "@/entrypoints/sidepanel/components/layout/web-index.tsx";
import classNames from "classnames";
import { NOTE_DETAIL_STORAGE_KEY, NOTE_MODIFY_STORAGE_KEY } from "@/utils/browserStorageCurrentPage.ts";
import { getLocationNotes, getQueryString, getUrlNoQuery, initNotelist, setNote } from "@/utils/notes";
import { getUserInfo, getToken, getTenantId } from "@/utils/auth";
import { Button, Collapse, ConfigProvider, Divider, Flex, message, Modal, Spin, theme, Tooltip } from "antd";
import { createStrokeService } from "../annotator";
import { DoubleLeftOutlined, DoubleRightOutlined } from "@ant-design/icons";
import { PinIcon } from "@/config/menu";
import { SHADOW_SIDE_PANEL, SIDE_PANEL_WRAPPER_ID } from "@/entrypoints/content/sidepanel";
import styles from "./index.module.less";
import { PermissionProvider } from "@/entrypoints/sidepanel/components/PermissionProvider";
import { useFetchRequest } from "@/hooks/useFetchRequest";
import { cacheSet } from "@/utils/browserStorage";
import useSSEChat from "@/hooks/useSSEChat";
import ReactDOM from "react-dom/client";
import ReactMarkdown from "react-markdown";
import "./fileRule.less";
import rehypeRaw from "rehype-raw"; // 支持 HTML（如 <font>）
import remarkGfm from "remark-gfm"; // 支持表格语法
import { StyleProvider } from "@ant-design/cssinjs";
import themeToken from "@/theme.json";

const { useToken } = theme;

const OpenImage = () => (
  <img
    draggable={false}
    className={classNames(styles["btn-wrapper-img"])}
    src={browser.runtime.getURL("/images/logo.png")}
    alt=""
  />
);

const getStyleContent = () => {
  // @ts-expect-error: 引用路径
  return fetch(browser.runtime.getURL("/content-scripts/content.css"))
    .then((response) => {
      if (response.ok) {
        return response.text();
      }
      throw new Error("无法加载样式文件");
    })
    .catch((error) => {
      console.error(error);
      return Promise.resolve(""); // 返回空样式或错误处理
    });
};

const CloseImage = () => (
  <svg transform="rotate(45)" width="20" height="20" viewBox="0 0 11 11" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      d="M10.353 4.853H6.14719V0.647003C6.14719 0.258763 5.88824 0 5.5 0C5.11176 0 4.853 0.258763 4.853 0.647003V4.853H0.647003C0.258763 4.853 0 5.11176 0 5.5C0 5.88824 0.258763 6.147 0.647003 6.147H4.853V10.353C4.853 10.7412 5.11176 11 5.5 11C5.88824 11 6.147 10.7412 6.147 10.353V6.14719H10.353C10.7412 6.14719 11 5.88843 11 5.50019C11.0002 5.11176 10.7412 4.853 10.353 4.853Z"
      fill="#bbb"
    />
  </svg>
);
const SidePanelWrapper: React.FC<{ container: HTMLDivElement }> = ({ container }) => {
  // const sseChat = useSSEChat();
  const { token } = useToken();
  let offsetX: number, offsetY: number;
  const toggleWrapperRef = useRef<HTMLDivElement>(null);
  const [dragging, setDragging] = useState<boolean>(false);
  const [expand, setExpand] = useState<boolean>(false);
  const [list, setList] = useState<any>([]);
  const currentUrl = useRef(getUrlNoQuery());
  const [open, setOpen] = useState<boolean>(false);
  const guestRef = useRef(null);
  const [showFormDetector, setShowFormDetector] = useState(false);
  const [showFormPanel, setShowFormPanel] = useState(false);
  const [isUserLoggedIn, setIsUserLoggedIn] = useState(false);
  const formCheckTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // 监听页面表单元素
  useEffect(() => {
    const checkForForms = () => {
      let hasFormElements = false;

      // 1. 检查传统的form标签
      const forms = document.querySelectorAll("form");
      if (forms.length > 0) {
        hasFormElements = true;
      }

      // 2. 检查不在form标签内的表单输入元素
      if (!hasFormElements) {
        const formInputs = document.querySelectorAll(
          'input[type="text"], input[type="email"], input[type="password"], input[type="tel"], input[type="url"], input[type="search"], input[type="number"], input[type="date"], input[type="datetime-local"], input[type="time"], input[type="month"], input[type="week"], input[type="color"], input[type="range"], input[type="file"], input[type="hidden"], input[type="radio"], input[type="checkbox"], textarea, select',
        );

        // 过滤掉一些明显不是表单的输入框（如搜索框、工具栏等）
        const validInputs = Array.from(formInputs).filter((input) => {
          const element = input as HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement;

          // 排除一些常见的非表单输入框
          const excludeSelectors = [
            '[role="search"]',
            ".search-input",
            ".toolbar-input",
            ".filter-input",
            '[data-testid*="search"]',
            '[placeholder*="搜索"]',
            '[placeholder*="search"]',
            '[class*="search"]',
            '[id*="search"]',
          ];

          // 检查是否匹配排除条件
          for (const selector of excludeSelectors) {
            if (element.matches(selector)) {
              return false;
            }
          }

          // 检查父元素是否包含搜索相关的类名或属性
          let parent = element.parentElement;
          let level = 0;
          while (parent && level < 3) {
            if (
              parent.className &&
              (parent.className.includes("search") ||
                parent.className.includes("toolbar") ||
                parent.className.includes("filter"))
            ) {
              return false;
            }
            parent = parent.parentElement;
            level++;
          }

          return true;
        });

        if (validInputs.length > 0) {
          hasFormElements = true;
        }
      }

      // 3. 检查iframe中的表单元素
      if (!hasFormElements) {
        try {
          const iframes = document.querySelectorAll("iframe");
          for (const iframe of iframes) {
            try {
              // 尝试访问iframe内容（同源策略限制）
              const iframeDoc = iframe.contentDocument || iframe.contentWindow?.document;
              if (iframeDoc) {
                const iframeForms = iframeDoc.querySelectorAll("form");
                const iframeInputs = iframeDoc.querySelectorAll(
                  'input[type="text"], input[type="email"], input[type="password"], input[type="tel"], input[type="url"], input[type="search"], input[type="number"], input[type="date"], input[type="datetime-local"], input[type="time"], input[type="month"], input[type="week"], input[type="color"], input[type="range"], input[type="file"], input[type="hidden"], input[type="radio"], input[type="checkbox"], textarea, select',
                );

                if (iframeForms.length > 0 || iframeInputs.length > 0) {
                  hasFormElements = true;
                  break;
                }
              }
            } catch (e) {
              // 跨域iframe无法访问，忽略错误
              console.debug("无法访问iframe内容（可能是跨域）:", e);
            }
          }
        } catch (e) {
          console.debug("检查iframe时出错:", e);
        }
      }

      setShowFormDetector(hasFormElements);
    };

    // 初始检查
    checkForForms();

    // 使用MutationObserver监听DOM变化
    const observer = new MutationObserver((mutations) => {
      // 检查是否有相关的DOM变化
      let shouldRecheck = false;

      for (const mutation of mutations) {
        if (mutation.type === "childList") {
          // 检查新增或删除的节点是否包含表单元素
          const addedNodes = Array.from(mutation.addedNodes);
          const removedNodes = Array.from(mutation.removedNodes);

          for (const node of [...addedNodes, ...removedNodes]) {
            if (node.nodeType === Node.ELEMENT_NODE) {
              const element = node as Element;
              if (
                element.tagName === "FORM" ||
                element.tagName === "INPUT" ||
                element.tagName === "TEXTAREA" ||
                element.tagName === "SELECT" ||
                element.tagName === "IFRAME" ||
                element.querySelector("form, input, textarea, select, iframe")
              ) {
                shouldRecheck = true;
                break;
              }
            }
          }
        }

        if (shouldRecheck) break;
      }

      if (shouldRecheck) {
        // 使用防抖避免频繁检查
        if (formCheckTimeoutRef.current) {
          clearTimeout(formCheckTimeoutRef.current);
        }
        formCheckTimeoutRef.current = setTimeout(checkForForms, 300);
      }
    });

    observer.observe(document.body, {
      childList: true,
      subtree: true,
    });

    // 监听 FormDetector 关闭消息
    const handleMessage = (event) => {
      if (event.data.type === "FORM_DETECTOR_CLOSED") {
        setShowFormPanel(false);
      }
    };

    window.addEventListener("message", handleMessage);

    return () => {
      observer.disconnect();
      window.removeEventListener("message", handleMessage);
      if (formCheckTimeoutRef.current) {
        clearTimeout(formCheckTimeoutRef.current);
      }
    };
  }, []);

  const isRuleFile = useRef(false); // 默认不开通规则
  const isRuleFun = useRef(1);

  const isFileCheck = useRef(false); // 默认不开通校验

  // const [isCollecting, setIsCollecting] = useState<boolean>(false);

  // 提取表单数据的核心函数
  const handleExtractFormData = useCallback(() => {
    const allFormData = [];
    let globalFieldIndex = 0;
    const excludeSelectors = [
      '[role="search"]',
      ".search-input",
      ".toolbar-input",
      ".filter-input",
      '[data-testid*="search"]',
      '[placeholder*="搜索"]',
      '[placeholder*="search"]',
      '[class*="search"]',
      '[id*="search"]',
    ].join(",");

    // 优化标签查找逻辑
    const getFieldLabel = (element: HTMLElement) => {
      // 1. 标准label标签查找
      if (element.id) {
        const label = document.querySelector(`label[for="${element.id}"]`);
        if (label) return label.textContent?.trim() || null;
      }

      // 2. 父元素内文本内容提取
      const container = element.closest("div, td, th, li, span, label");
      if (container) {
        // 查找包含冒号的文本
        const textContent = container.textContent?.trim() || "";
        const labelMatch = textContent.match(/([\u4e00-\u9fa5]+[\u4e00-\u9fa5\s]*?)\s*[：:]/);
        if (labelMatch) return labelMatch[1];

        // 查找包含星号的必填项标识
        const asteriskElement = container.querySelector('font[color="red"], span[style*="color:red"]');
        if (asteriskElement) {
          const labelText = asteriskElement.previousSibling?.textContent?.trim();
          if (labelText) return labelText;
        }
      }

      // 3. 前一个兄弟元素文本
      let prevSibling = element.previousElementSibling;
      while (prevSibling) {
        const text = prevSibling.textContent?.trim();
        if (text && !/^[\d\s*\-_]+$/.test(text)) return text;
        prevSibling = prevSibling.previousElementSibling;
      }

      // 4. 表格单元格标签
      const cell = element.closest("td, th");
      if (cell) {
        const prevCell = cell.previousElementSibling;
        if (prevCell) return prevCell.textContent?.trim() || null;
      }

      // 5. 使用placeholder作为备选
      return (element as HTMLInputElement).placeholder || null;
    };

    // 处理表单元素的通用函数（性能优化版）
    const processFormElement = (element: HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement, formInfo: any) => {
      // 排除搜索类元素
      if (element.matches(excludeSelectors)) return;

      // 检查父元素是否包含排除类名
      let parent = element.parentElement;
      let level = 0;
      while (parent && level < 2) {
        // 减少遍历深度
        if (parent.matches(excludeSelectors)) return;
        parent = parent.parentElement;
        level++;
      }

      globalFieldIndex++;
      const fieldData = {
        fieldIndex: globalFieldIndex,
        tagName: element.tagName.toLowerCase(),
        type: element.type || null,
        name: element.name || null,
        id: element.id || null,
        placeholder: (element as HTMLInputElement).placeholder || null,
        label: getFieldLabel(element),
        value: null,
      };

      // 获取字段值（优化逻辑）
      if (element.type === "checkbox" || element.type === "radio") {
        fieldData.value = (element as HTMLInputElement).checked ? element.value || "checked" : null;
      } else if (element.tagName.toLowerCase() === "select") {
        const selectElement = element as HTMLSelectElement;
        fieldData.value = selectElement.options[selectElement.selectedIndex]?.textContent?.trim() || null;
      } else {
        fieldData.value = element.value?.trim() || null;
      }

      formInfo.fields.push(fieldData);
    };

    // 1. 处理传统form标签（使用更高效的选择器）
    document.querySelectorAll("form").forEach((form, formIndex) => {
      const formInfo: any = {
        formIndex: formIndex + 1,
        formAction: form.action || null,
        formMethod: form.method || null,
        formType: "traditional",
        fields: [],
      };

      form.querySelectorAll("input, select, textarea").forEach((element) => {
        processFormElement(element as HTMLInputElement, formInfo);
      });

      if (formInfo.fields.length > 0) allFormData.push(formInfo);
    });

    // 2. 处理独立表单元素（优化选择器）
    const standaloneTypes = [
      "text",
      "email",
      "password",
      "tel",
      "url",
      "search",
      "number",
      "date",
      "datetime-local",
      "time",
      "month",
      "week",
      "color",
      "range",
      "file",
      "hidden",
      "radio",
      "checkbox",
    ];

    const standaloneSelector = standaloneTypes
      .map((type) => `input[type="${type}"]:not(form input)`)
      .concat(["textarea:not(form textarea)", "select:not(form select)"])
      .join(",");

    const standaloneElements = document.querySelectorAll(standaloneSelector);
    if (standaloneElements.length > 0) {
      const standaloneFormInfo: any = {
        formIndex: allFormData.length + 1,
        formAction: null,
        formMethod: null,
        formType: "standalone",
        fields: [],
      };

      standaloneElements.forEach((element) => {
        processFormElement(element as HTMLInputElement, standaloneFormInfo);
      });

      if (standaloneFormInfo.fields.length > 0) {
        allFormData.push(standaloneFormInfo);
      }
    }

    // 3. 处理iframe中的表单（异步处理）
    const processIframe = (iframe: HTMLIFrameElement, iframeIndex: number) => {
      try {
        const iframeDoc = iframe.contentDocument || iframe.contentWindow?.document;
        if (!iframeDoc) return;

        // 处理iframe中的form标签
        iframeDoc.querySelectorAll("form").forEach((form, formIndex) => {
          const iframeFormInfo: any = {
            formIndex: allFormData.length + 1,
            formAction: form.action || null,
            formMethod: form.method || null,
            formType: "iframe",
            iframeIndex: iframeIndex + 1,
            fields: [],
          };

          form.querySelectorAll("input, select, textarea").forEach((element) => {
            processFormElement(element as HTMLInputElement, iframeFormInfo);
          });

          if (iframeFormInfo.fields.length > 0) allFormData.push(iframeFormInfo);
        });

        // 处理iframe中独立表单元素
        const iframeStandaloneElements = iframeDoc.querySelectorAll(standaloneSelector);
        if (iframeStandaloneElements.length > 0) {
          const iframeStandaloneFormInfo: any = {
            formIndex: allFormData.length + 1,
            formAction: null,
            formMethod: null,
            formType: "iframe-standalone",
            iframeIndex: iframeIndex + 1,
            fields: [],
          };

          iframeStandaloneElements.forEach((element) => {
            processFormElement(element as HTMLInputElement, iframeStandaloneFormInfo);
          });

          if (iframeStandaloneFormInfo.fields.length > 0) {
            allFormData.push(iframeStandaloneFormInfo);
          }
        }
      } catch (e) {
        console.debug(`无法访问iframe ${iframeIndex + 1}内容（可能是跨域）:`, e);
      }
    };

    // 使用requestIdleCallback处理iframe避免阻塞主线程
    const iframes = document.querySelectorAll("iframe");
    iframes.forEach((iframe, i) => {
      if (iframe.contentDocument) {
        processIframe(iframe, i);
      } else {
        iframe.addEventListener("load", () => processIframe(iframe, i));
      }
    });

    return {
      pageUrl: window.location.href,
      pageTitle: document.title,
      extractTime: new Date().toISOString(),
      formsCount: allFormData.length,
      forms: allFormData,
    };
  }, []);

  // --- 新增：文件上传大小限制逻辑 ---
  useEffect(() => {
    // 1. 从 chrome.storage.local 加载配置
    chrome.storage.local.get("fileRule", (data) => {
      isRuleFile.current = !!data?.fileRule?.isFileRule;
      isRuleFun.current = data?.fileRule?.ruleFun || 1;
      isFileCheck.current = !!data?.fileRule?.isFileCheck;
    });
    chrome.storage.onChanged.addListener((changes, namespace) => {
      // // console.log("1111namespace", namespace);
      if (namespace === "local") {
        chrome.storage.local.get("fileRule", (data) => {
          isRuleFile.current = !!data?.fileRule?.isFileRule;
          isRuleFun.current = data?.fileRule?.ruleFun || 1;
          isFileCheck.current = !!data?.fileRule?.isFileCheck;
        });
      }
    });
    // 2. 检查文件是否需要阻断
    async function checkAndBlock(files: any): Promise<boolean> {
      // // console.log("isRuleFile", isRuleFile.current);
      // // console.log("isFileCheck", isFileCheck.current);
      if (!isRuleFile.current && !isFileCheck.current) {
        return false; // 未启用规则，放行
      } else {
        // setIsCollecting(true);
        try {
          let formDataString = "";
          if (isFileCheck.current) {
            const extractedData = handleExtractFormData();
            console.log("extractedData", extractedData);
            formDataString = JSON.stringify(extractedData); // 直接获取数据
          }

          const tokenInfo = await getToken();
          const tenantId = await getTenantId();
          const userInfo = await getUserInfo();
          const allowedTypes = [
            "txt",
            "md",
            "markdown",
            "pdf",
            "html",
            "xlsx",
            "xls",
            "docx",
            "csv",
            "eml",
            "msg",
            "pptx",
            "ppt",
            "xml",
            "epub",
            "jpg",
            "png",
            "jpeg",
            "gif",
            "webp",
            "svg",
          ];
          const fileArray = Array.from(files);
          // ✅ 过滤出合法的文件
          const validFiles = fileArray.filter((file: any) => {
            const ext = file.name.split(".").pop()?.toLowerCase() || "";
            return allowedTypes.includes(ext);
          });

          // ✅ 如果所有文件都非法 → 放行
          if (validFiles.length === 0) {
            return false;
          }

          const fileData: any[] = [];

          for (const file of validFiles) {
            const fileStr = await fileToBase64(file);

            const singleUpload = () =>
              new Promise<void>((resolve, reject) => {
                fetchRequest({
                  api: "uploadChatFile",
                  params: {
                    fileName: file.name,
                    fileStr,
                    loading: true,
                    path: "/files/upload",
                    agentId: "de8668b9-6072-4181-ae45-85f4cc098520",
                    user: userInfo?.id,
                  },
                  file: true,
                  callback: (res) => {
                    const imgType = ["jpg", "png", "jpeg", "gif", "webp", "svg"];
                    const audioType = ["mp3", "m4a", "wav", "webm", "amr"];
                    const videoType = ["mp4", "mov", "mpeg", "mpga"];
                    const ext = (res.data.extension || "").toLowerCase();
                    if (imgType.includes(ext)) {
                      res.data.fileType = "image";
                    } else if (audioType.includes(ext)) {
                      res.data.fileType = "audio";
                    } else if (videoType.includes(ext)) {
                      res.data.fileType = "video";
                    } else {
                      res.data.fileType = "document";
                    }
                    fileData.push({
                      type: res.data.fileType,
                      transfer_method: "local_file",
                      upload_file_id: res.data.id,
                    });
                    resolve(); // 标记该上传完成
                  },
                });
              });
            await singleUpload(); // 等 callback 完成再继续下一项
          }

          // return true;
          let resolveFn: ((v: boolean) => void) | null = null;
          return new Promise((resolve, reject) => {
            resolveFn = resolve; // 暂存 resolve 函数，供 Modal 中调用
            getStyleContent().then((data) => {
              const style = document.createElement("style");
              style.textContent = data;
              const shadowDom = document.createElement("shadow-dom-file");
              shadowDom.id = "shadow-side-file";
              shadowDom.style.position = "relative";
              shadowDom.style.zIndex = (2 ** 32 - 1).toString();
              const shadowRoot = shadowDom.attachShadow({ mode: "open" });

              // 注入基础样式
              shadowRoot.appendChild(style);

              const mask = document.createElement("div");
              mask.id = "agent-output-mask";
              // 2. 创建内容容器
              const container = document.createElement("div");
              container.id = "agent-output-container-file";
              container.className = "agent-output-container-file";
              // 3. 添加到 body（先遮罩，后内容）
              shadowRoot.appendChild(mask);
              shadowRoot.appendChild(container);

              // React Root
              const root = ReactDOM.createRoot(container);
              document.body.parentNode.appendChild(shadowDom);
              // // console.log("xiangfei", "进入1");
              // 用来管理组件状态的 React 组件
              const Wrapper: React.FC = () => {
                const sseChat = useSSEChat();
                const sseChat2 = useSSEChat();
                // const [content, setContent] = useState([]);
                // const [activeKeys, setActiveKeys] = useState<string[]>([]);
                const isAllPassed = useRef(null); // 用于存储定时器 ID
                const num = useRef(0); // 用于存储定时器 ID
                const [fileRuleContent, setFileRuleContent] = useState([]);
                const [fileCheckContent, setFileCheckContent] = useState([]);
                const [fileRuleActiveKeys, setFileRuleActiveKeys] = useState<string[]>([]);
                const [fileCheckActiveKeys, setFileCheckActiveKeys] = useState<string[]>([]);
                // 添加状态存储失败文件名
                const [failedFiles, setFailedFiles] = useState<string[]>([]);

                // const [finalResult, setFinalResult] = useState<string>("");
                // const [fileRuleContent, setFileRuleContent] = useState([]);
                // const [fileCheckContent, setFileCheckContent] = useState([]);
                // 确定
                const handleConfirm = () => {
                  let flag = false;
                  // console.log("failedFiles", failedFiles);
                  if (isFileCheck.current && !isRuleFile.current) {
                    flag = failedFiles.length === 0 ? false : true;
                  } else if (!isFileCheck.current && isRuleFile.current) {
                    flag = isAllPassed.current ? true : false;
                  } else {
                    flag = !isAllPassed.current && failedFiles.length === 0 ? false : true;
                  }
                  root.unmount();
                  const shadowHost = document.getElementById("shadow-side-file");
                  if (shadowHost && shadowHost.parentNode) {
                    shadowHost.parentNode.removeChild(shadowHost);
                  }
                  resolveFn?.(flag); // false: 文件通过 true: 文件阻断
                };
                // 关闭
                const handleClose = () => {
                  let flag = false;

                  if (isFileCheck.current && !isRuleFile.current) {
                    flag = failedFiles.length === 0 ? false : true;
                  } else if (!isFileCheck.current && isRuleFile.current) {
                    flag = isAllPassed.current ? true : false;
                  } else {
                    flag = !isAllPassed.current && failedFiles.length === 0 ? false : true;
                  }

                  root.unmount();
                  document.getElementById("shadow-side-file")?.remove();
                  resolveFn?.(flag); // false: 文件通过 true: 文件阻断
                };
                // 样式处理
                const parseLabel = (raw: string) => {
                  const match = raw.match(/(.*?)：<font color="(.*?)">(.*?)<\/font>/);
                  if (match) {
                    const [, title, color, status] = match;
                    return (
                      <>
                        {title}：<span style={{ color }}>{status}</span>
                      </>
                    );
                  }
                  return raw;
                };
                useEffect(() => {
                  // 防止已经加载的情况下，打开新网页可能拿的是默认值
                  // // console.log("xiangfei", "进入2");
                  fileData.map((item) => {
                    if (item.type == "image") {
                      num.current++;
                    }
                  });

                  // // console.log("isRuleFun.current", isRuleFun.current);
                  // // console.log("num.current", num.current);

                  if (isRuleFile.current) {
                    sseChat.start({
                      message: "1",
                      url: "/dify/broker/agent/stream",
                      headers: {
                        "Content-Type": "application/json",
                        Token: tokenInfo,
                      },
                      body: {
                        insId: "1",
                        bizType: "app:agent",
                        bizId: "de8668b9-6072-4181-ae45-85f4cc098520",
                        agentId: "de8668b9-6072-4181-ae45-85f4cc098520",
                        path: "/chat-messages",
                        difyJson: {
                          inputs: {
                            query: "规则审核",
                            personalLibs: [],
                            Token: tokenInfo,
                            tenantid: tenantId,
                            type: num.current > 0 ? "正则" : isRuleFun.current == 1 ? "正则" : "AI",
                          },
                          files: fileData,
                          response_mode: "streaming",
                          user: userInfo?.id,
                          conversation_id: "",
                          auto_generate_name: "true",
                          query: "规则审核",
                        },
                      },
                      query: {
                        query: "规则审核",
                        conversation_id: "",
                      },
                      onMessage: (res) => {
                        // // console.log("规则审核", res);
                        // 1. 去除 <think> 标签
                        if (isRuleFun.current == 2 && num.current == 0) {
                          const markdown = res.replace(/<think>[\s\S]*?<\/think>/gi, "").trim();
                          // // console.log("markdown", markdown);
                          // 2. 提取每一段 Collapse 数据块（假设每段以一级标题开头）
                          const sections = markdown.split(/^#\s+/gm).filter(Boolean);
                          const hasFailed = markdown.includes("不通过"); // ✅ 检测是否有“不通过”字样
                          // // console.log("sections", sections);
                          isAllPassed.current = hasFailed; // ✅ 存储检测结果

                          // // console.log('sections11111', sections)
                          const parsedItems = sections.map((section, index) => {
                            const [rawTitle, ...rest] = section.split("\n");
                            const fileRuleContent = rest.join("\n").trim();

                            return {
                              key: String(index + 1),
                              label: parseLabel(rawTitle.trim()), // Collapse 的标题
                              children: (
                                <ReactMarkdown
                                  remarkPlugins={[remarkGfm]}
                                  rehypePlugins={[rehypeRaw]} // 支持 HTML
                                >
                                  {fileRuleContent}
                                </ReactMarkdown>
                              ),
                            };
                          });
                          // setContent(parsedItems);
                          // setActiveKeys(parsedItems.map((item) => item.key));
                          // setFileRuleContent(parsedItems);
                          // 替换 setContent 和 setActiveKeys
                          setFileRuleContent(parsedItems);
                          setFileRuleActiveKeys(parsedItems.map((item) => item.key));
                        } else {
                          // 直接设置为空数组，触发 loading 显示
                          setFileRuleContent([]);
                          setFileRuleActiveKeys([]);
                          return;
                        }
                      },
                      onFinished: (res) => {
                        // json格式
                        if (isRuleFun.current == 1 || num.current > 0) {
                          const markdown = res.replace(/<think>[\s\S]*?<\/think>/gi, "").trim();
                          // 正则检索
                          // 2. 把多个 JSON 对象拼成合法 JSON 数组
                          const fixedJson = `[${markdown.replace(/}\s*{/g, "},{")}]`;
                          let parsedArray: any[] = [];
                          try {
                            parsedArray = JSON.parse(fixedJson);
                          } catch (err) {
                            console.error("解析 JSON 出错", err);
                            return;
                          }

                          // 3. 检查是否有“不通过”
                          const hasFailed = parsedArray.some((item) => item.key === "不通过");
                          isAllPassed.current = hasFailed;

                          // 4. 构造 Collapse 所需格式
                          const parsedItems = parsedArray.map((item, index) => {
                            const status = item.key || "未知状态";
                            const color = status === "通过" ? "green" : "red";
                            const title = (
                              <span>
                                {item.filename || "无名文件"}: <span style={{ color }}>{status}</span>
                              </span>
                            );
                            const info = item.info || item[" "] || "无内容";

                            return {
                              key: String(index + 1),
                              label: title,
                              children: (
                                <ReactMarkdown remarkPlugins={[remarkGfm]} rehypePlugins={[rehypeRaw]}>
                                  {info}
                                </ReactMarkdown>
                              ),
                            };
                          });
                          setFileRuleContent(parsedItems);
                          setFileRuleActiveKeys(parsedItems.map((item) => item.key));
                        }
                      },
                      instruct: "start",
                    });
                  }
                  if (isFileCheck.current) {
                    sseChat2.start({
                      message: "1",
                      url: "/dify/broker/agent/stream",
                      headers: {
                        "Content-Type": "application/json",
                        Token: tokenInfo,
                      },
                      body: {
                        insId: "1",
                        bizType: "app:agent",
                        bizId: "17c6281d-074b-4802-9654-41bf3ee6dd49",
                        agentId: "17c6281d-074b-4802-9654-41bf3ee6dd49",
                        path: "/chat-messages",
                        difyJson: {
                          inputs: {
                            type: "AI",
                          },
                          files: fileData,
                          response_mode: "streaming",
                          user: userInfo?.id,
                          conversation_id: "",
                          auto_generate_name: "true",
                          query: formDataString,
                        },
                      },
                      query: {
                        query: "合同校验",
                        conversation_id: "",
                      },
                      onMessage: (res) => {
                        // console.log("合同校验", res);
                        const cleanedRes = res
                          .replace(/```markdown/g, "")
                          .replace(/```/g, "")
                          .replace(/<\/?think>/g, "")
                          .trim();

                        // 提取所有表格部分
                        const tables = [];
                        const tableRegex = /(\|.+\|[\s\S]+?)(?=\n\n|$)/g;
                        let tableMatch;

                        while ((tableMatch = tableRegex.exec(cleanedRes)) !== null) {
                          if (tableMatch[1].includes("|")) {
                            tables.push(tableMatch[1]);
                          }
                        }

                        // 提取每个表格的校验结果
                        const tableResults: Record<number, string> = {};
                        const resultRegex = /最终校验结果[:：]\s*(\S+)/g;
                        let resultMatch;
                        let resultIndex = 0;

                        while ((resultMatch = resultRegex.exec(cleanedRes)) !== null) {
                          // 过滤掉#号
                          let finalResult = resultMatch[1] || "";
                          finalResult = finalResult.replace(/\*/g, "");

                          tableResults[resultIndex] = finalResult;
                          resultIndex++;
                        }

                        // 收集未通过校验的文件名
                        const failed = [];
                        for (let i = 0; i < validFiles.length; i++) {
                          const finalResult = tableResults[i] || "";
                          if (finalResult.includes("不一致") || finalResult.includes("无法判断")) {
                            failed.push(validFiles[i]);
                          }
                        }
                        setFailedFiles(failed);

                        // 转换为Collapse面板需要的格式
                        const parsedItems = tables.map((table, index) => {
                          // console.log('tableResults', tableResults)
                          const finalResult = tableResults[index] || "";

                          return {
                            key: `table-${index + 1}`,
                            label: `合同${index + 1}校验结果`,
                            children: (
                              <div className="contract-check-container">
                                <div className="markdown-content">
                                  <ReactMarkdown
                                    remarkPlugins={[remarkGfm]}
                                    rehypePlugins={[rehypeRaw]}
                                    components={{
                                      table: ({ node, ...props }) => <table className="contract-table" {...props} />,
                                      thead: ({ node, ...props }) => <thead className="contract-thead" {...props} />,
                                      th: ({ node, ...props }) => <th className="contract-th" {...props} />,
                                      tr: ({ node, ...props }) => <tr className="contract-tr" {...props} />,
                                      td: ({ node, ...props }) => {
                                        // 处理三种状态：一致、不一致、无法判断
                                        // 递归获取文本内容
                                        const getTextContent = (children: React.ReactNode): string => {
                                          if (typeof children === "string") {
                                            return children;
                                          }
                                          if (Array.isArray(children)) {
                                            return children.map(getTextContent).join("");
                                          }
                                          if (children && typeof children === "object" && "props" in children) {
                                            return getTextContent(children.props.children);
                                          }
                                          return "";
                                        };

                                        const content = getTextContent(props.children);

                                        // 处理三种状态：一致、不一致、无法判断
                                        const isInconsistent = content.includes("×") || content.includes("不一致");
                                        const isUndetermined = content.includes("无法判断") || content.includes("?");

                                        return (
                                          <td
                                            className={
                                              isUndetermined
                                                ? "contract-td undetermined"
                                                : isInconsistent
                                                  ? "contract-td inconsistent"
                                                  : "contract-td consistent"
                                            }
                                          >
                                            <span> {props.children}</span>
                                          </td>
                                        );
                                      },
                                    }}
                                  >
                                    {table}
                                  </ReactMarkdown>
                                </div>

                                {finalResult && (
                                  <div
                                    className={
                                      finalResult.includes("无法判断")
                                        ? "final-result3"
                                        : finalResult.includes("不一致")
                                          ? "final-result"
                                          : "final-result2"
                                    }
                                  >
                                    <span
                                      className={
                                        finalResult.includes("无法判断")
                                          ? "final-result-label3"
                                          : finalResult.includes("不一致")
                                            ? "final-result-label"
                                            : "final-result-label2"
                                      }
                                    >
                                      最终校验结果：
                                    </span>
                                    <span
                                      className={
                                        finalResult.includes("无法判断")
                                          ? "final-result-value3"
                                          : finalResult.includes("不一致")
                                            ? "final-result-value"
                                            : "final-result-value2"
                                      }
                                    >
                                      {finalResult}
                                    </span>
                                  </div>
                                )}
                              </div>
                            ),
                          };
                        });

                        setFileCheckContent(parsedItems);
                        setFileCheckActiveKeys(parsedItems.map((item) => item.key));
                      },
                      instruct: "start",
                    });
                  }
                }, []);
                // // console.log("xiangfei", "进入3");
                // 规则检验Modal
                if (isRuleFile.current && !isFileCheck.current) {
                  return (
                    <StyleProvider hashPriority="high" container={shadowRoot as any}>
                      <ConfigProvider theme={themeToken}>
                        <Modal
                          title="文件检测"
                          open={true}
                          className="file-rule-modal"
                          width={880}
                          onOk={handleConfirm}
                          onCancel={handleClose}
                          maskClosable={false}
                          keyboard={false}
                          okText="确定"
                          cancelButtonProps={{ style: { display: "none" } }}
                          getContainer={() => {
                            if (document.getElementById("shadow-side-file")) {
                              const shadowDom = document.getElementById("shadow-side-file")?.shadowRoot;
                              return shadowDom?.querySelector("#agent-output-container-file") as HTMLElement;
                            }
                            return document.body;
                          }}
                        >
                          {fileRuleContent && fileRuleContent.length === 0 ? (
                            <div style={{ textAlign: "center", padding: "40px 0" }}>
                              <Spin tip="文件检测中..." size="large" />
                            </div>
                          ) : (
                            <Collapse
                              className={`file-rule-info${
                                isRuleFun.current == 2 && num.current == 0 ? " file-rule-table" : ""
                              }`}
                              items={fileRuleContent}
                              activeKey={fileRuleActiveKeys}
                              onChange={(keys) => setFileRuleActiveKeys(Array.isArray(keys) ? keys : [keys])}
                            />
                          )}
                        </Modal>
                      </ConfigProvider>
                    </StyleProvider>
                  );
                }
                // 合同校验Modal
                if (isFileCheck.current && !isRuleFile.current) {
                  return (
                    <StyleProvider hashPriority="high" container={shadowRoot as any}>
                      <ConfigProvider theme={themeToken}>
                        <Modal
                          title="合同校验报告"
                          open={true}
                          className="file-rule-modal"
                          width={880}
                          onOk={handleConfirm}
                          onCancel={handleClose}
                          maskClosable={false}
                          keyboard={false}
                          okText="确定"
                          cancelButtonProps={{ style: { display: "none" } }}
                          getContainer={() => {
                            if (document.getElementById("shadow-side-file")) {
                              const shadowDom = document.getElementById("shadow-side-file")?.shadowRoot;
                              return shadowDom?.querySelector("#agent-output-container-file") as HTMLElement;
                            }
                            return document.body;
                          }}
                        >
                          <div className="report-section">
                            <h3 style={{ marginBottom: 16 }}>合同校验报告</h3>
                            <span className="subTitle">以下是合同文件与系统信息的对比校验结果</span>
                            {fileCheckContent.length === 0 ? (
                              <div style={{ textAlign: "center", padding: "20px 0" }}>
                                <Spin tip="合同校验中..." size="large" />
                              </div>
                            ) : (
                              <Collapse
                                items={fileCheckContent}
                                activeKey={fileCheckActiveKeys}
                                onChange={(keys) => setFileCheckActiveKeys(Array.isArray(keys) ? keys : [keys])}
                              />
                            )}
                          </div>
                        </Modal>
                      </ConfigProvider>
                    </StyleProvider>
                  );
                }
                // 规则检验和合同校验共同Modal
                if (isFileCheck.current && isRuleFile.current) {
                  return (
                    <StyleProvider hashPriority="high" container={shadowRoot as any}>
                      <ConfigProvider theme={themeToken}>
                        <Modal
                          title="文件检测与合同校验报告"
                          open={true}
                          className="file-rule-modal"
                          width={880}
                          onOk={handleConfirm}
                          onCancel={handleClose}
                          maskClosable={false}
                          keyboard={false}
                          okText="确定"
                          cancelButtonProps={{ style: { display: "none" } }}
                          getContainer={() => {
                            if (document.getElementById("shadow-side-file")) {
                              const shadowDom = document.getElementById("shadow-side-file")?.shadowRoot;
                              return shadowDom?.querySelector("#agent-output-container-file") as HTMLElement;
                            }
                            return document.body;
                          }}
                        >
                          {/* 文件检测规则区域 */}
                          <div className="report-section">
                            <h3 style={{ marginBottom: 16 }}>文件检测报告</h3>
                            {fileRuleContent.length === 0 ? (
                              <div style={{ textAlign: "center", padding: "20px 0" }}>
                                <Spin tip="文件检测中..." size="large" />
                              </div>
                            ) : (
                              <Collapse
                                className={`file-rule-info${
                                  isRuleFun.current == 2 && num.current == 0 ? " file-rule-table" : ""
                                }`}
                                items={fileRuleContent}
                                activeKey={fileRuleActiveKeys}
                                onChange={(keys) => setFileRuleActiveKeys(Array.isArray(keys) ? keys : [keys])}
                              />
                            )}
                          </div>

                          {/* 分隔线 */}
                          <Divider style={{ margin: "24px 0" }} />

                          {/* 合同校验区域 */}
                          <div className="report-section">
                            <h3 style={{ marginBottom: 16 }}>合同校验报告</h3>
                            <span className="subTitle">以下是合同文件与系统信息的对比校验结果</span>
                            {fileCheckContent.length === 0 ? (
                              <div style={{ textAlign: "center", padding: "20px 0" }}>
                                <Spin tip="合同校验中..." size="large" />
                              </div>
                            ) : (
                              <Collapse
                                items={fileCheckContent}
                                activeKey={fileCheckActiveKeys}
                                onChange={(keys) => setFileCheckActiveKeys(Array.isArray(keys) ? keys : [keys])}
                              />
                            )}
                          </div>
                        </Modal>
                      </ConfigProvider>
                    </StyleProvider>
                  );
                }
              };

              // 渲染 Wrapper 组件
              root.render(<Wrapper />);
            });
          });
        } catch (error) {
          console.error("收集表单数据失败:", error);
          message.error("收集表单数据失败，请重试");
        } finally {
          // setIsCollecting(false);
        }
      }
    }

    // 4. 事件监听包裹函数，确保异步 await 生效
    const onInputFileChangeWrapper = (e: Event) => {
      // // console.log("e", e);
      const target = e.target as HTMLInputElement;
      if (!target?.files || target.files.length === 0) return;
      // 如果是“重发”的事件，跳过，防止死循环
      if ((e as any)._fromCheckAndBlock) return;
      e.preventDefault();
      e.stopImmediatePropagation();
      (async () => {
        const shouldBlock = await checkAndBlock(target.files);
        console.log("change 阻止:", shouldBlock);
        if (shouldBlock) {
          setTimeout(() => {
            target.value = "";
          }, 0);
          return;
        }
        // for (const file of target.files) {

        // }
        // 触发新的 change 事件让后续逻辑继续
        const newEvent = new Event("change", {
          bubbles: true,
          cancelable: true,
        });
        (newEvent as any)._fromCheckAndBlock = true;
        target.dispatchEvent(newEvent);
      })();
    };

    const onFormSubmitWrapper = (e: Event) => {
      const form = e.target as HTMLFormElement;
      if ((e as any)._fromCheckAndBlock) return; // 跳过标记事件

      const inputs = form.querySelectorAll('input[type="file"]');
      let hasFile = false;
      for (const input of inputs) {
        const files = (input as HTMLInputElement).files;
        if (files && files.length > 0) {
          hasFile = true;
          break;
        }
      }
      if (!hasFile) return;

      e.preventDefault();
      e.stopImmediatePropagation();
      (async () => {
        for (const input of inputs) {
          const files = (input as HTMLInputElement).files;
          if (!files) continue;
          const shouldBlock = await checkAndBlock(files);
          // console.log("submit 阻止:", shouldBlock);
          if (shouldBlock) {
            return; // 阻止提交
          }
          // for (const file of files) {
          // }
        }
        // 触发新的submit事件，带标记避免递归
        const newEvent = new Event("submit", {
          bubbles: true,
          cancelable: true,
        });
        (newEvent as any)._fromCheckAndBlock = true;
        form.dispatchEvent(newEvent);
      })();
    };

    const onDropWrapper = (e: DragEvent) => {
      if ((e as any)._fromCheckAndBlock) return; // 跳过标记事件

      const hasFile = Array.from(e.dataTransfer?.items || []).some((item) => item.kind === "file");
      if (!hasFile) return;

      e.preventDefault();
      e.stopImmediatePropagation();
      (async () => {
        const shouldBlock = await checkAndBlock(e.dataTransfer!.files);
        // console.log("drop 阻止:", shouldBlock);
        if (shouldBlock) {
          return;
        }
        // for (const file of e.dataTransfer!.files) {

        // }
        // 触发新的 drop 事件，带标记避免递归
        const newEvent = new DragEvent("drop", {
          bubbles: true,
          cancelable: true,
          dataTransfer: e.dataTransfer!,
        });
        (newEvent as any)._fromCheckAndBlock = true;
        e.target?.dispatchEvent(newEvent);
      })();
    };

    document.addEventListener("change", onInputFileChangeWrapper, true);
    document.addEventListener("submit", onFormSubmitWrapper, true);
    document.addEventListener("drop", onDropWrapper, true);

    // 5. 拦截 fetch 上传
    const originalFetch = window.fetch;
    window.fetch = async function (...args) {
      const [resource, init] = args;
      if (init?.body instanceof FormData) {
        for (const value of init.body.values()) {
          if (value instanceof File) {
            const shouldBlock = await checkAndBlock(value);
            // console.log("fetch 阻止:", shouldBlock);
            if (shouldBlock) {
              return Promise.reject(new Error("文件上传被阻止"));
            }
          }
        }
      }
      return originalFetch.apply(this, args as any);
    };

    // 6. 拦截 XMLHttpRequest 上传，改成 async/await 包装
    const originalXHRSend = XMLHttpRequest.prototype.send;

    XMLHttpRequest.prototype.send = function (...args: any[]) {
      if (args[0] instanceof FormData) {
        const body = args[0];

        (async () => {
          for (const value of body.values()) {
            if (value instanceof File) {
              const shouldBlock = await checkAndBlock(value);
              // console.log("xhr 阻止:", shouldBlock);
              if (shouldBlock) {
                this.abort(); // 👈 直接用 this，不要 xhr
                return;
              }
            }
          }
          originalXHRSend.apply(this, args); // 👈 也直接用 this
        })();
      } else {
        originalXHRSend.apply(this, args);
      }
    };

    // 7. 清理
    return () => {
      document.removeEventListener("change", onInputFileChangeWrapper, true);
      document.removeEventListener("submit", onFormSubmitWrapper, true);
      document.removeEventListener("drop", onDropWrapper, true);
      window.fetch = originalFetch;
      XMLHttpRequest.prototype.send = originalXHRSend;
    };
  }, []);

  // 文件转base64
  function fileToBase64(file) {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();

      // 成功读取文件时的回调
      reader.onload = () => {
        resolve(reader.result); // Base64 编码的字符串
      };

      // 读取文件失败时的回调
      reader.onerror = (error) => {
        reject(error);
      };

      // 读取文件并转为 Base64
      reader.readAsDataURL(file);
    });
  }

  useEffect(() => {
    getUserInfo().then((res) => {
      if (res) {
        setIsUserLoggedIn(true);
        getNotes("init");
      } else {
        setIsUserLoggedIn(false);
      }
    });

    let sinoKey = sessionStorage.getItem("sino-tap-key");
    const handleNoteListChanged = async (changes) => {
      let noteInfo = changes[NOTE_MODIFY_STORAGE_KEY + sinoKey];
      if (noteInfo) {
        getNotes(noteInfo.newValue.updateType, noteInfo.newValue.id);
      }
      // 展开
      if (
        changes[NOTE_DETAIL_STORAGE_KEY + sinoKey] ||
        changes["characterDataTime"] ||
        changes["characterData"] ||
        changes["chatKnowLocal"]
      ) {
        container.classList.add(styles["expand"]);
        const shadowDom = document.getElementById(SHADOW_SIDE_PANEL).shadowRoot;
        const div = shadowDom.querySelector<HTMLDivElement>(`#${SIDE_PANEL_WRAPPER_ID}`);
        let width = (await browser.storage.local.get("siderWidth"))?.siderWidth || 400;
        div.style.transform = "translateX(0px)";
        div.style.width = width + "px";
        setExpand(true);
      }
      if (changes["userInfo"]) {
        // 登录
        if (changes["userInfo"].newValue) {
          setIsUserLoggedIn(true);
          getNotes("init");
        }
        // 登出
        if (changes["userInfo"].oldValue) {
          setIsUserLoggedIn(false);
          setList([]);
          getLocationNotes().then((noteList) => {
            guestRef.current._delAllNoteNotice(noteList.map((x) => x.id));
          });
        }
      }
    };
    browser.storage.local.onChanged.addListener(handleNoteListChanged);
    return () => {
      browser.storage.local.onChanged.removeListener(handleNoteListChanged);
    };
  }, []);

  // 监听主页面 URL 变化
  useEffect(() => {
    let flag = false;
    let observer: MutationObserver | null = null;

    // 检查用户是否已登录
    const checkUserLoginAndStartObserver = async () => {
      const userInfo = await getUserInfo();

      // 只有在用户已登录时才启动URL监听
      if (userInfo) {
        console.debug("用户已登录，启动URL变化监听");

        // 创建一个 MutationObserver 实例来监视指定节点的子节点变化
        observer = new MutationObserver(async (mutationsList) => {
          // 再次检查用户登录状态
          const currentUserInfo = await getUserInfo();
          if (!currentUserInfo) {
            console.debug("用户已退出登录，停止URL变化处理");
            return;
          }

          if (flag) {
            // 如果标志位为true，则停止进一步的处理
            return;
          }
          flag = true;

          for (let mutation of mutationsList) {
            if (mutation.type === "childList" && currentUrl.current !== getUrlNoQuery()) {
              // 在这里可以执行一些操作，处理 URL 变化事件
              currentUrl.current = getUrlNoQuery();
              const noteList: any = await getCurrentLocationList(currentUrl.current, "init");
              setList(noteList);
              guestRef.current?._delAllNoteNotice(noteList.map((x) => x.id));
              initNotelist(guestRef.current, noteList);
              break;
            }
          }

          // 完成处理后，重置标志位
          flag = false;
        });

        // 选择要监视变化的节点，可以根据具体情况选择合适的节点
        const targetNode = document.body;

        // 配置 MutationObserver 实例以监视子节点的变化
        const observerConfig = { childList: true, subtree: true };

        observer.observe(targetNode, observerConfig);
      } else {
        console.debug("用户未登录，不启动URL变化监听");
      }
    };

    checkUserLoginAndStartObserver();

    return () => {
      if (observer) {
        observer.disconnect();
        observer = null;
      }
    };
  }, []);
  const fetchRequest = useFetchRequest();
  // 获取当前页便签，存储到 local
  const getCurrentLocationList = async (url: string, type?: string) => {
    // 检查用户是否已登录
    const userInfo = await getUserInfo();
    if (!userInfo) {
      console.debug("用户未登录，跳过便签获取");
      return [];
    }

    const noteId = getQueryString("noteId") || "";
    return new Promise((resolve) => {
      fetchRequest({
        api: "listNote",
        params: {
          query: "",
          url,
          noteId: noteId,
        },
        callback: (res) => {
          if (res.code === 200) {
            const list = (res.data || []).filter((item: any, index: number, self: any[]) => {
              return self.findIndex((t: any) => t.id === item.id) === index;
            });
            if (type === "init") {
              list.forEach((item: any) => {
                item.displayFlag = 1;
              });
            }
            setNote(list);
            cacheSet("noteList", list);
            resolve(list);
          } else {
            message.open({
              type: "error",
              content: res.msg,
            });
            resolve([]);
          }
        },
      });
    });
  };

  const setNoteShowClose = async (type: boolean) => {
    if (list.length === 0) return;
    // const promises = list.map((note) => {
    //   note.displayFlag = type ? "1" : "2";
    //   return fetchRequest({
    //     api: "editNote",
    //     params: note,
    //     callback: () => {},
    //   });
    // });

    // try {
    //   await Promise.all(promises);
    //   getNotes("edit");
    //   guestRef.current && guestRef.current._setNoteShowClose(type ? "1" : "2", type);
    // } catch (error) {
    //   console.error("请求出错", error);
    // }
    guestRef.current && guestRef.current._setNoteShowClose(type ? "1" : "2", type);
  };

  const getNotes = async (type?: string, id?: string) => {
    const noteList: any = await getCurrentLocationList(currentUrl.current, type);
    setList(noteList);
    if (!guestRef.current) {
      guestRef.current = createStrokeService(noteList);
    } else {
      if (type === "init") {
        initNotelist(guestRef.current, noteList);
      }
    }
  };

  const handleToggleBtnClick = async () => {
    const shadowDom = document.getElementById(SHADOW_SIDE_PANEL).shadowRoot;
    const div = shadowDom.querySelector<HTMLDivElement>(`#${SIDE_PANEL_WRAPPER_ID}`);
    let width = (await browser.storage.local.get("siderWidth"))?.siderWidth || 400;
    if (expand) {
      div.style.transform = `translateX(${width}px)`;
      container.classList.remove(styles["expand"]);
      setExpand(false);
    } else {
      container.classList.add(styles["expand"]);
      div.style.transform = "translateX(0px)";
      div.style.width = width + "px";
      setExpand(true);
    }
  };

  const handleDrag = (e) => {
    toggleWrapperRef.current.style.left = e.clientX - offsetX + "px";
    toggleWrapperRef.current.style.top = e.clientY - offsetY + "px";
  };

  const handleToggleBtnDragStart = (e) => {
    setDragging(true);
    offsetX = e.clientX - toggleWrapperRef.current.getBoundingClientRect().left;
    offsetY = e.clientY - toggleWrapperRef.current.getBoundingClientRect().top;
    document.addEventListener("dragover", handleDrag);
  };

  const handleToggleBtnDragEnd = () => {
    setDragging(false);
    document.removeEventListener("dragover", handleDrag);
    toggleWrapperRef.current.style.left = "0";
  };

  return (
    <PermissionProvider>
      <div
        id="annotator-stroke"
        style={{
          position: "absolute",
          zIndex: 9,
          pointerEvents: "none",
          opacity: expand ? 1 : 0,
        }}
      />
      <Flex
        className={classNames(styles["toggle-btn-wrapper"])}
        ref={toggleWrapperRef}
        style={{ bottom: "125px" }}
        vertical
      >
        {/* 表单检测按钮组 */}
        {/* {isUserLoggedIn && showFormDetector && (
          <Flex
            style={{
              marginBottom: "10px",
              position: "absolute",
              left: "-51px",
              top: "-16px",
              zIndex: "10000 !important",
              width: "31px",
              height: "20px",
              borderRadius: "9999px",
              background: "#fff",
              boxShadow: "0 2px 10px rgba(0, 0, 0, 0.08)",
              display: "flex",
              flexDirection: "column",
              justifyContent: "center",
              alignItems: "center",
              padding: "20px 10px",
            }}
            vertical
          >
            {/* 收集表单按钮 */}
        {/* <div
              onClick={() => {
                const newShowPanel = !showFormPanel;
                setShowFormPanel(newShowPanel);
                // 向主页面发送消息控制表单检测面板，并触发表单数据提取
                window.postMessage(
                  {
                    type: "TOGGLE_FORM_DETECTOR",
                    show: newShowPanel,
                    extractFormData: true, // 新增标志，表示需要提取表单数据
                  },
                  "*",
                );
              }}
              title="收集表单数据"
              style={{ textAlign: "center", cursor: "pointer" }}
            >
              <img
                style={{ width: "25px", height: "22px" }}
                src={browser.runtime.getURL("/images/setup/InboxOutlined.svg")}
                alt=""
              />
              <span style={{ color: "#757272", fontSize: 12 }}>收集</span>
            </div> */}
        {/* <Divider
              orientationMargin="0"
              // 移除 size 属性，因为 Divider 组件不支持该属性
              style={{
                margin: 10,
                // 移除上下外边距，避免与内容间距
                borderBlockWidth: "2px",
                backgroundColor: "#ccc8c8", // 浅灰色，与常见分割线配色一致（可根据图片微调）
              }}
            /> */}
        {/* 填充表单按钮 */}
        {/* <div
              onClick={() => {
                // 向主页面发送消息触发表单填充
                window.postMessage(
                  {
                    type: "FILL_FORM_DATA",
                    action: "fill",
                  },
                  "*",
                );
              }}
              title="智能填充表单"
              style={{ textAlign: "center", cursor: "pointer" }}
            >
              <img
                style={{ width: "25px", height: "25px" }}
                src={browser.runtime.getURL("/images/setup/WriteOutlined.svg")}
                alt=""
              />
              <span style={{ color: "#757272", fontSize: 12 }}>填充</span>
            </div> */}
        {/* </Flex> 
        )} */}
        {list.length > 0 && (
          <Flex>
            {open && (
              <Flex
                onMouseEnter={() => {
                  setOpen(true);
                }}
                onMouseLeave={() => {
                  setOpen(false);
                }}
                style={{
                  position: "absolute",
                  left: "-48px",
                  top: "-84px",
                  zIndex: "10000 !important",
                  paddingBottom: "8px",
                }}
              >
                <Flex
                  vertical
                  style={{
                    width: "40px",
                    borderRadius: "8px",
                    boxShadow:
                      "rgba(0, 0, 0, 0.08) 0px 6px 16px 0px, rgba(0, 0, 0, 0.12) 0px 3px 6px -4px, rgba(0, 0, 0, 0.05) 0px 9px 28px 8px",
                    background: token.colorBgBase,
                    fontSize: token.fontSizeSM,
                  }}
                  align="center"
                >
                  <Tooltip
                    placement="left"
                    title="一键展开"
                    getPopupContainer={(triggerNode) => triggerNode.parentNode as any}
                  >
                    <Button
                      icon={<DoubleLeftOutlined />}
                      style={{ padding: token.paddingXS, margin: "4px" }}
                      type="text"
                      onClick={() => setNoteShowClose(true)}
                    ></Button>
                  </Tooltip>
                  <Divider orientationMargin="0" style={{ margin: 0 }} />
                  <Tooltip
                    placement="left"
                    title="一键收起"
                    getPopupContainer={(triggerNode) => triggerNode.parentNode as any}
                  >
                    <Button
                      icon={<DoubleRightOutlined />}
                      style={{ padding: token.paddingXS, margin: "4px" }}
                      type="text"
                      onClick={() => setNoteShowClose(false)}
                    ></Button>
                  </Tooltip>
                </Flex>
              </Flex>
            )}
            <Tooltip
              placement="left"
              title={`本页便签： ${list.length}`}
              getPopupContainer={(triggerNode) => triggerNode.parentNode as any}
            >
              <Flex
                align="center"
                justify="center"
                gap="3"
                style={{
                  position: "absolute",
                  left: "-48px",
                  zIndex: "10000 !important",
                  width: "40px",
                  height: "40px",
                  borderRadius: "8px",
                  cursor: "pointer",
                  boxShadow:
                    "0px 6px 16px 0px rgba(0, 0, 0, 0.08),0px 3px 6px -4px rgba(0, 0, 0, 0.12),0px 9px 28px 8px rgba(0, 0, 0, 0.05)",
                  background: token.colorBgBase,
                  fontSize: token.fontSizeSM,
                }}
                onMouseEnter={() => {
                  setOpen(true);
                }}
                onMouseLeave={() => {
                  setOpen(false);
                }}
              >
                <Flex
                  style={{
                    transform: "scale(0.608)",
                  }}
                >
                  {PinIcon}
                </Flex>
                <Flex>{list.length}</Flex>
              </Flex>
            </Tooltip>
          </Flex>
        )}
        <Flex
          className={classNames([
            styles[expand ? "toggle-btn-expand" : "toggle-btn"],
            styles[dragging && "dragging"],
            expand && styles["expand"],
          ])}
          draggable={!expand}
          onClick={handleToggleBtnClick}
          onDragStart={handleToggleBtnDragStart}
          onDragEnd={handleToggleBtnDragEnd}
        >
          {expand ? <CloseImage /> : <OpenImage />}
        </Flex>
      </Flex>
      <SidePanelLayout />

      <style>
        {`
          @keyframes pulse {
            0% {
              transform: scale(1);
            }
            50% {
              transform: scale(1.1);
            }
            100% {
              transform: scale(1);
            }
          }
        `}
      </style>
    </PermissionProvider>
  );
};

export default SidePanelWrapper;
