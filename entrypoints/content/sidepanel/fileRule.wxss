.agent-output-container-file .file-rule-modal .report-section {
  margin-bottom: 24px;
}
.agent-output-container-file .file-rule-modal .report-section h3 {
  font-size: 16px;
  font-weight: 600;
  color: #1890ff;
  border-left: 3px solid #1890ff;
  padding-left: 8px;
}
.agent-output-container-file .file-rule-modal .ant-modal-body {
  max-height: 1000px;
  overflow-y: auto;
}
.agent-output-container-file .file-rule-modal .ant-modal-body .file-rule-info {
  margin-bottom: 20px;
  border: none;
  background: none !important;
  border-radius: 0px;
}
.agent-output-container-file .file-rule-modal .ant-modal-body .file-rule-info .ant-collapse-content {
  border-top: 1px solid #ebeef5;
}
.agent-output-container-file .file-rule-modal .ant-modal-body .file-rule-info .ant-collapse-content-box {
  padding: 0px !important;
}
.agent-output-container-file .file-rule-modal .ant-modal-body .file-rule-info .ant-collapse-content-box p {
  margin: 0px;
  padding: 0px;
  min-height: 24px;
  line-height: 24px;
  padding: 20px 20px 20px 20px;
}
.agent-output-container-file .file-rule-modal .ant-modal-body .file-rule-info .ant-collapse-item {
  background: #f5f7fa;
  margin-bottom: 20px;
  border-radius: 4px;
  border: 1px solid #ebeef5;
}
.agent-output-container-file .file-rule-modal .ant-modal-body .file-rule-table .ant-collapse-item:last-child {
  border-bottom: 0px;
}
.agent-output-container-file .file-rule-modal table {
  width: 100%;
  border: none;
  border-spacing: 0px;
  margin-top: 12px;
}
.agent-output-container-file .file-rule-modal table thead th {
  background: #f5f7fa;
  font-weight: 500;
  padding: 10px;
  border-bottom: 1px solid #ebeef5;
}
.agent-output-container-file .file-rule-modal table thead th:nth-child(1) {
  color: #409eff;
}
.agent-output-container-file .file-rule-modal table tbody td {
  padding: 10px;
  color: #606266;
  border-bottom: 1px solid #ebeef5;
}
.agent-output-container-file .file-rule-modal table tbody td:nth-child(1) {
  color: #409eff;
}
.agent-output-container-file .subTitle {
  color: #2c3e50;
  font-size: 14px;
  margin: 0 0 10px 10px;
  display: inline-block;
}
.agent-output-container-file .contract-check-container .markdown-content .contract-table {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: 30px;
  font-size: 14px;
}
.agent-output-container-file .contract-check-container .markdown-content .contract-table .contract-thead {
  background-color: #f0f5ff;
}
.agent-output-container-file .contract-check-container .markdown-content .contract-table .contract-thead .contract-th {
  text-align: left;
  font-weight: 700;
  color: #2c3e50;
  border: 1px solid #dcdfe6;
  font-size: 16px;
}
.agent-output-container-file .contract-check-container .markdown-content .contract-table .contract-thead .contract-th:not(:last-child)::after {
  content: "";
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  height: 16px;
  width: 1px;
  background-color: #d9d9d9;
}
.agent-output-container-file .contract-check-container .markdown-content .contract-table .contract-thead .contract-th:first-child {
  width: 15%;
}
.agent-output-container-file .contract-check-container .markdown-content .contract-table .contract-thead .contract-th:last-child {
  width: 15%;
}
.agent-output-container-file .contract-check-container .markdown-content .contract-table .contract-tr {
  border-bottom: 1px solid #e8e8e8;
}
.agent-output-container-file .contract-check-container .markdown-content .contract-table .contract-tr:nth-child(even) {
  background-color: #f7fafc;
}
.agent-output-container-file .contract-check-container .markdown-content .contract-table .contract-tr .contract-td {
  padding: 16px 12px;
  color: #333333;
  line-height: 1.6;
  border: 1px solid #e8e8e8;
}
.agent-output-container-file .contract-check-container .markdown-content .contract-table .contract-tr .contract-td:first-child span {
  font-weight: 700;
  color: #2c3e50;
  font-size: 16px;
}
.agent-output-container-file .contract-check-container .markdown-content .contract-table .contract-tr .contract-td:last-child.inconsistent {
  color: #f5222d;
  font-weight: 500;
  position: relative;
  padding-left: 35px;
}
.agent-output-container-file .contract-check-container .markdown-content .contract-table .contract-tr .contract-td:last-child.inconsistent::before {
  content: "×";
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  width: 16px;
  height: 16px;
  background-color: #f5222d;
  color: #ffffff;
  border-radius: 2px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: bold;
}
.agent-output-container-file .contract-check-container .markdown-content .contract-table .contract-tr .contract-td:last-child.consistent {
  color: green;
  font-weight: 500;
  position: relative;
  padding-left: 35px;
}
.agent-output-container-file .contract-check-container .markdown-content .contract-table .contract-tr .contract-td:last-child.consistent::before {
  content: "✓";
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  width: 16px;
  height: 16px;
  background-color: green;
  color: #ffffff;
  border-radius: 2px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: bold;
}
.agent-output-container-file .contract-check-container .markdown-content .contract-table .contract-tr .contract-td:last-child.undetermined {
  color: #faad14;
  font-weight: 500;
  position: relative;
  padding-left: 35px;
}
.agent-output-container-file .contract-check-container .markdown-content .contract-table .contract-tr .contract-td:last-child.undetermined::before {
  content: "?";
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  width: 16px;
  height: 16px;
  background-color: #faad14;
  color: #ffffff;
  border-radius: 2px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: bold;
}
.agent-output-container-file .contract-check-container .final-result {
  justify-content: space-between;
  padding: 16px;
  background-color: #fff0f0;
  border: 1px solid #ffccc7;
  border-radius: 4px;
  display: flex;
  align-items: center;
}
.agent-output-container-file .contract-check-container .final-result .final-result-label {
  color: #f5222d;
  margin-right: 8px;
  font-size: 16px;
  font-weight: 700;
}
.agent-output-container-file .contract-check-container .final-result .final-result-value {
  font-size: 14px;
  font-weight: 700;
  padding: 4px 8px;
  background-color: #f5222d;
  color: #ffffff;
  border-radius: 5px;
  position: relative;
  padding-left: 24px;
  margin-right: 28px;
}
.agent-output-container-file .contract-check-container .final-result .final-result-value::before {
  content: "×";
  position: absolute;
  left: 8px;
  top: 50%;
  transform: translateY(-50%);
  width: 14px;
  height: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: bold;
}
.agent-output-container-file .contract-check-container .final-result2 {
  justify-content: space-between;
  padding: 16px;
  background-color: #e0f2e0;
  border: 1px solid green;
  border-radius: 4px;
  display: flex;
  align-items: center;
}
.agent-output-container-file .contract-check-container .final-result2 .final-result-label2 {
  color: green;
  margin-right: 8px;
  font-size: 16px;
  font-weight: 700;
}
.agent-output-container-file .contract-check-container .final-result2 .final-result-value2 {
  font-size: 14px;
  font-weight: 700;
  padding: 4px 8px;
  background-color: green;
  color: #ffffff;
  border-radius: 5px;
  position: relative;
  padding-left: 24px;
  margin-right: 28px;
}
.agent-output-container-file .contract-check-container .final-result2 .final-result-value2::before {
  content: "√";
  position: absolute;
  left: 8px;
  top: 50%;
  transform: translateY(-50%);
  width: 14px;
  height: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: bold;
}
.agent-output-container-file .contract-check-container .final-result3 {
  justify-content: space-between;
  padding: 16px;
  background-color: #fceed2;
  border: 1px solid #fcf2dd;
  border-radius: 4px;
  display: flex;
  align-items: center;
}
.agent-output-container-file .contract-check-container .final-result3 .final-result-label3 {
  color: #faad14;
  margin-right: 8px;
  font-size: 16px;
  font-weight: 700;
}
.agent-output-container-file .contract-check-container .final-result3 .final-result-value3 {
  font-size: 14px;
  font-weight: 700;
  padding: 4px 8px;
  background-color: #faad14;
  color: #ffffff;
  border-radius: 5px;
  position: relative;
  padding-left: 24px;
  margin-right: 28px;
}
.agent-output-container-file .contract-check-container .final-result3 .final-result-value3::before {
  content: "?";
  position: absolute;
  left: 8px;
  top: 50%;
  transform: translateY(-50%);
  width: 14px;
  height: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: bold;
}
